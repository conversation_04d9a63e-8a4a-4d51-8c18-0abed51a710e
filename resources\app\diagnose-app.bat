@echo off
chcp 65001 >nul
title تشخيص مشاكل التطبيق - Future Fuel Corporation

echo ========================================
echo    تشخيص مشاكل التطبيق
echo    Future Fuel Corporation
echo ========================================
echo.

echo [1/8] فحص Node.js...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js مثبت
    node --version
) else (
    echo ❌ Node.js غير مثبت
    echo يرجى تنزيل وتثبيت Node.js من: https://nodejs.org
    echo.
    goto :end
)

echo.
echo [2/8] فحص npm...
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ npm متوفر
    npm --version
) else (
    echo ❌ npm غير متوفر
    echo.
    goto :end
)

echo.
echo [3/8] فحص مجلد node_modules...
if exist "node_modules" (
    echo ✅ مجلد node_modules موجود
) else (
    echo ❌ مجلد node_modules غير موجود
    echo يجب تشغيل: npm install
    echo.
)

echo.
echo [4/8] فحص تثبيت Electron...
npm list electron --depth=0 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Electron مثبت
    npm list electron --depth=0
) else (
    echo ❌ Electron غير مثبت
    echo يجب تشغيل: npm install electron --save-dev
    echo.
)

echo.
echo [5/8] فحص ملفات التطبيق الأساسية...
if exist "package.json" (
    echo ✅ package.json موجود
) else (
    echo ❌ package.json غير موجود
)

if exist "main.js" (
    echo ✅ main.js موجود
) else (
    echo ❌ main.js غير موجود
)

if exist "index.html" (
    echo ✅ index.html موجود
) else (
    echo ❌ index.html غير موجود
)

if exist "preload.js" (
    echo ✅ preload.js موجود
) else (
    echo ❌ preload.js غير موجود
)

echo.
echo [6/8] فحص الأيقونات...
if exist "icons\app-icon.ico" (
    echo ✅ أيقونة التطبيق موجودة
) else (
    echo ❌ أيقونة التطبيق غير موجودة
)

echo.
echo [7/8] فحص اختصار سطح المكتب...
if exist "%USERPROFILE%\Desktop\Future Fuel Corporation.lnk" (
    echo ✅ اختصار سطح المكتب موجود
) else (
    echo ❌ اختصار سطح المكتب غير موجود
)

echo.
echo [8/8] فحص العمليات الجارية...
tasklist /FI "IMAGENAME eq electron.exe" 2>nul | find /I "electron.exe" >nul
if %errorlevel% equ 0 (
    echo ✅ Electron يعمل في الخلفية
    tasklist /FI "IMAGENAME eq electron.exe"
) else (
    echo ⚠️  Electron غير يعمل حالياً
)

echo.
echo ========================================
echo تم الانتهاء من التشخيص
echo ========================================
echo.

echo الحلول المقترحة:
echo.
echo إذا كان Electron غير مثبت:
echo   npm install
echo.
echo لتشغيل التطبيق:
echo   npm start
echo   أو
echo   start-app.bat
echo.
echo لإعادة إنشاء اختصار سطح المكتب:
echo   powershell -ExecutionPolicy Bypass -File create-shortcut-now.ps1
echo.

:end
pause
