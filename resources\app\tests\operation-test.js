// اختبار نظام العمليات التلقائي
// Operation System Test

class OperationSystemTest {
    constructor() {
        this.testResults = [];
        this.totalTests = 0;
        this.passedTests = 0;
        this.failedTests = 0;
    }

    /**
     * تشغيل جميع اختبارات نظام العمليات
     */
    async runOperationTests() {
        console.log('🧪 بدء اختبارات نظام العمليات التلقائي...');
        
        try {
            // اختبار تهيئة النظام
            await this.testSystemInitialization();
            
            // اختبار إضافة عملية تركيب
            await this.testInstallationOperation();
            
            // اختبار إضافة عملية مراقبة
            await this.testMonitoringOperation();
            
            // اختبار إضافة عملية تجديد بطاقة
            await this.testCardRenewalOperation();
            
            // اختبار تكامل جدول الإرسال
            await this.testTransmissionTableIntegration();
            
            // اختبار تكامل بطاقات الغاز
            await this.testGasCardsIntegration();
            
            // عرض النتائج
            this.showTestResults();
            
        } catch (error) {
            console.error('❌ خطأ في تشغيل اختبارات نظام العمليات:', error);
        }
    }

    /**
     * اختبار تهيئة النظام
     */
    async testSystemInitialization() {
        console.log('🔧 اختبار تهيئة نظام العمليات...');
        
        // اختبار وجود مدير العمليات
        this.test('وجود مدير العمليات', () => {
            return typeof window.operationManager !== 'undefined';
        });

        // اختبار تهيئة مدير العمليات
        this.test('تهيئة مدير العمليات', () => {
            return window.operationManager && window.operationManager.isInitialized;
        });

        // اختبار وجود إصلاح التكامل
        this.test('وجود إصلاح التكامل', () => {
            return typeof window.operationIntegrationFix !== 'undefined';
        });

        // اختبار وجود نموذج الزبون
        this.test('وجود نموذج الزبون', () => {
            return document.getElementById('customer-form') !== null;
        });

        // اختبار وجود حقل نوع العملية
        this.test('وجود حقل نوع العملية', () => {
            return document.getElementById('operation-type') !== null;
        });
    }

    /**
     * اختبار عملية التركيب
     */
    async testInstallationOperation() {
        console.log('🔧 اختبار عملية التركيب...');
        
        // محاكاة بيانات عملية التركيب
        const testData = {
            operationType: 'تركيب',
            operationDate: '2024-01-15',
            operationNotes: 'تركيب خزان غاز جديد',
            customerData: {
                name: 'أحمد محمد',
                phone: '0123456789',
                email: '<EMAIL>',
                address: 'الرياض'
            },
            vehicleData: {
                plateNumber: 'ABC-123',
                brand: 'تويوتا',
                model: 'كامري',
                year: '2020',
                color: 'أبيض'
            },
            tankData: {
                type: 'أسطوانة',
                brand: 'الوطنية',
                serialNumber: 'TK-2024-001',
                capacity: '60'
            }
        };

        // اختبار إضافة العملية إلى جدول الإرسال
        this.test('إضافة عملية التركيب إلى جدول الإرسال', () => {
            try {
                if (window.operationIntegrationFix) {
                    // محاكاة إضافة العملية
                    const initialCount = window.appData?.transmissionTable?.length || 0;
                    
                    // إضافة العملية
                    window.operationIntegrationFix.addOperationToTransmissionTable(testData);
                    
                    const newCount = window.appData?.transmissionTable?.length || 0;
                    return newCount > initialCount;
                }
                return false;
            } catch (error) {
                console.error('خطأ في اختبار عملية التركيب:', error);
                return false;
            }
        });
    }

    /**
     * اختبار عملية المراقبة
     */
    async testMonitoringOperation() {
        console.log('👁️ اختبار عملية المراقبة...');
        
        // محاكاة بيانات عملية المراقبة
        const testData = {
            operationType: 'مراقبة',
            operationDate: '2024-01-16',
            operationNotes: 'مراقبة دورية للخزان',
            customerData: {
                name: 'فاطمة علي',
                phone: '0987654321',
                email: '<EMAIL>',
                address: 'جدة'
            },
            vehicleData: {
                plateNumber: 'XYZ-456',
                brand: 'هوندا',
                model: 'أكورد',
                year: '2019',
                color: 'أسود'
            },
            tankData: {
                type: 'أسطوانة',
                brand: 'السعودية',
                serialNumber: 'TK-2024-002',
                capacity: '80'
            }
        };

        // اختبار إضافة العملية إلى جدول الإرسال
        this.test('إضافة عملية المراقبة إلى جدول الإرسال', () => {
            try {
                if (window.operationIntegrationFix) {
                    const initialCount = window.appData?.transmissionTable?.length || 0;
                    
                    window.operationIntegrationFix.addOperationToTransmissionTable(testData);
                    
                    const newCount = window.appData?.transmissionTable?.length || 0;
                    return newCount > initialCount;
                }
                return false;
            } catch (error) {
                console.error('خطأ في اختبار عملية المراقبة:', error);
                return false;
            }
        });
    }

    /**
     * اختبار عملية تجديد البطاقة
     */
    async testCardRenewalOperation() {
        console.log('💳 اختبار عملية تجديد البطاقة...');
        
        // محاكاة بيانات عملية تجديد البطاقة
        const testData = {
            operationType: 'تجديد',
            operationDate: '2024-01-17',
            operationNotes: 'تجديد بطاقة الغاز',
            customerData: {
                name: 'محمد أحمد',
                phone: '0555123456',
                email: '<EMAIL>',
                address: 'الدمام'
            },
            vehicleData: {
                plateNumber: 'DEF-789',
                brand: 'نيسان',
                model: 'التيما',
                year: '2021',
                color: 'فضي'
            },
            tankData: {
                type: 'أسطوانة',
                brand: 'الخليج',
                serialNumber: 'TK-2024-003',
                capacity: '70'
            }
        };

        // اختبار إضافة البطاقة إلى بطاقات الغاز
        this.test('إضافة بطاقة غاز جديدة', () => {
            try {
                if (window.operationIntegrationFix) {
                    const initialCount = window.appData?.gasCards?.length || 0;
                    
                    window.operationIntegrationFix.addOperationToGasCards(testData);
                    
                    const newCount = window.appData?.gasCards?.length || 0;
                    return newCount > initialCount;
                }
                return false;
            } catch (error) {
                console.error('خطأ في اختبار تجديد البطاقة:', error);
                return false;
            }
        });
    }

    /**
     * اختبار تكامل جدول الإرسال
     */
    async testTransmissionTableIntegration() {
        console.log('📋 اختبار تكامل جدول الإرسال...');
        
        // اختبار وجود وظيفة تحديث جدول الإرسال
        this.test('وجود وظيفة تحديث جدول الإرسال', () => {
            return typeof window.updateTransmissionTable === 'function';
        });

        // اختبار وجود بيانات جدول الإرسال
        this.test('وجود بيانات جدول الإرسال', () => {
            return window.appData && Array.isArray(window.appData.transmissionTable);
        });

        // اختبار عدد العمليات في جدول الإرسال
        this.test('وجود عمليات في جدول الإرسال', () => {
            return window.appData?.transmissionTable?.length > 0;
        });
    }

    /**
     * اختبار تكامل بطاقات الغاز
     */
    async testGasCardsIntegration() {
        console.log('💳 اختبار تكامل بطاقات الغاز...');
        
        // اختبار وجود وظيفة تحديث بطاقات الغاز
        this.test('وجود وظيفة تحديث بطاقات الغاز', () => {
            return typeof window.updateGasCardsTable === 'function';
        });

        // اختبار وجود بيانات بطاقات الغاز
        this.test('وجود بيانات بطاقات الغاز', () => {
            return window.appData && Array.isArray(window.appData.gasCards);
        });

        // اختبار عدد البطاقات
        this.test('وجود بطاقات في النظام', () => {
            return window.appData?.gasCards?.length > 0;
        });
    }

    /**
     * تشغيل اختبار واحد
     */
    test(name, testFunction) {
        this.totalTests++;
        
        try {
            const result = testFunction();
            
            if (result) {
                this.passedTests++;
                this.testResults.push({ name, status: 'PASS', error: null });
                console.log(`✅ ${name}`);
            } else {
                this.failedTests++;
                this.testResults.push({ name, status: 'FAIL', error: 'Test returned false' });
                console.log(`❌ ${name}`);
            }
        } catch (error) {
            this.failedTests++;
            this.testResults.push({ name, status: 'ERROR', error: error.message });
            console.log(`💥 ${name}: ${error.message}`);
        }
    }

    /**
     * عرض نتائج الاختبارات
     */
    showTestResults() {
        console.log('\n📊 نتائج اختبارات نظام العمليات:');
        console.log('='.repeat(50));
        console.log(`إجمالي الاختبارات: ${this.totalTests}`);
        console.log(`نجح: ${this.passedTests} ✅`);
        console.log(`فشل: ${this.failedTests} ❌`);
        console.log(`معدل النجاح: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%`);
        
        if (this.failedTests > 0) {
            console.log('\n❌ الاختبارات الفاشلة:');
            this.testResults
                .filter(test => test.status !== 'PASS')
                .forEach(test => {
                    console.log(`- ${test.name}: ${test.error || 'فشل'}`);
                });
        }
        
        // عرض ملخص العمليات
        this.showOperationsSummary();
        
        // عرض إشعار للمستخدم
        if (typeof window.showEnhancedToast === 'function') {
            const message = `اختبارات نظام العمليات: ${this.passedTests}/${this.totalTests} نجح`;
            const type = this.failedTests === 0 ? 'success' : 'warning';
            window.showEnhancedToast(message, type, 5000);
        }
        
        return {
            total: this.totalTests,
            passed: this.passedTests,
            failed: this.failedTests,
            results: this.testResults
        };
    }

    /**
     * عرض ملخص العمليات
     */
    showOperationsSummary() {
        console.log('\n📋 ملخص العمليات:');
        
        const transmissionCount = window.appData?.transmissionTable?.length || 0;
        const gasCardsCount = window.appData?.gasCards?.length || 0;
        
        console.log(`- عمليات في جدول الإرسال: ${transmissionCount}`);
        console.log(`- بطاقات الغاز: ${gasCardsCount}`);
        
        if (transmissionCount > 0) {
            const installationCount = window.appData.transmissionTable.filter(t => t.type === 'تركيب').length;
            const monitoringCount = window.appData.transmissionTable.filter(t => t.type === 'مراقبة').length;
            
            console.log(`  - عمليات التركيب: ${installationCount}`);
            console.log(`  - عمليات المراقبة: ${monitoringCount}`);
        }
    }

    /**
     * اختبار سريع لنظام العمليات
     */
    async quickOperationTest() {
        console.log('⚡ اختبار سريع لنظام العمليات...');
        
        const quickTests = [
            () => typeof window.operationManager !== 'undefined',
            () => typeof window.operationIntegrationFix !== 'undefined',
            () => document.getElementById('operation-type') !== null,
            () => typeof window.updateTransmissionTable === 'function',
            () => typeof window.updateGasCardsTable === 'function'
        ];
        
        const results = quickTests.map(test => test());
        const passed = results.filter(Boolean).length;
        
        console.log(`اختبار سريع للعمليات: ${passed}/${quickTests.length} مكونات جاهزة`);
        
        return passed === quickTests.length;
    }
}

// إنشاء مثيل من نظام اختبار العمليات
const operationSystemTest = new OperationSystemTest();

// تصدير النظام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = operationSystemTest;
} else {
    window.operationSystemTest = operationSystemTest;
}

// تشغيل اختبار سريع عند التحميل
if (typeof window !== 'undefined') {
    window.addEventListener('load', () => {
        setTimeout(() => {
            operationSystemTest.quickOperationTest();
        }, 3000);
    });
}
