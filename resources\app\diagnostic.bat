@echo off
title Future Fuel Management System - Diagnostic Tool
color 0E

echo =============================================
echo    Future Fuel Management System
echo           Diagnostic Tool v2.2.0
echo =============================================
echo.

echo [1/8] Checking system information...
echo OS: %OS%
echo Processor: %PROCESSOR_ARCHITECTURE%
echo User: %USERNAME%
echo Computer: %COMPUTERNAME%
echo.

echo [2/8] Checking file integrity...
set MISSING_FILES=0

if not exist "index.html" (
    echo ERROR: index.html missing
    set /a MISSING_FILES+=1
) else (
    echo OK: index.html found
)

if not exist "main.js" (
    echo ERROR: main.js missing
    set /a MISSING_FILES+=1
) else (
    echo OK: main.js found
)

if not exist "preload.js" (
    echo ERROR: preload.js missing
    set /a MISSING_FILES+=1
) else (
    echo OK: preload.js found
)

if not exist "package.json" (
    echo ERROR: package.json missing
    set /a MISSING_FILES+=1
) else (
    echo OK: package.json found
)

if not exist "scripts\script.js" (
    echo ERROR: scripts\script.js missing
    set /a MISSING_FILES+=1
) else (
    echo OK: scripts\script.js found
)

if not exist "scripts\transmission-table.js" (
    echo ERROR: scripts\transmission-table.js missing
    set /a MISSING_FILES+=1
) else (
    echo OK: scripts\transmission-table.js found
)

if not exist "styles\styles.css" (
    echo ERROR: styles\styles.css missing
    set /a MISSING_FILES+=1
) else (
    echo OK: styles\styles.css found
)

if not exist "assets\icons\app-icon.ico" (
    echo WARNING: app-icon.ico missing
) else (
    echo OK: app-icon.ico found
)

echo.
echo [3/8] Checking Node.js installation...
where node >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo OK: Node.js is installed
    node --version
) else (
    echo WARNING: Node.js not found
    echo Install from: https://nodejs.org/
)

echo.
echo [4/8] Checking npm installation...
where npm >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo OK: npm is installed
    npm --version
) else (
    echo WARNING: npm not found
)

echo.
echo [5/8] Checking dependencies...
if exist "node_modules" (
    echo OK: node_modules directory exists
) else (
    echo WARNING: node_modules not found
    echo Run: npm install
)

echo.
echo [6/8] Checking data files...
if exist "data.json" (
    echo OK: data.json found
    for %%A in (data.json) do echo Size: %%~zA bytes
) else (
    echo INFO: data.json not found (will be created on first run)
)

if exist "gasShopData*" (
    echo OK: localStorage backup files found
) else (
    echo INFO: No localStorage backup files found
)

echo.
echo [7/8] Checking browser compatibility...
echo Testing default browser...
start "" "https://caniuse.com/es6" >nul 2>&1
echo OK: Browser test launched

echo.
echo [8/8] Generating diagnostic report...
echo ============================================= > diagnostic_report.txt
echo Future Fuel Management System Diagnostic Report >> diagnostic_report.txt
echo Generated: %date% %time% >> diagnostic_report.txt
echo ============================================= >> diagnostic_report.txt
echo. >> diagnostic_report.txt
echo System Information: >> diagnostic_report.txt
echo OS: %OS% >> diagnostic_report.txt
echo Processor: %PROCESSOR_ARCHITECTURE% >> diagnostic_report.txt
echo User: %USERNAME% >> diagnostic_report.txt
echo Computer: %COMPUTERNAME% >> diagnostic_report.txt
echo. >> diagnostic_report.txt
echo File Integrity: >> diagnostic_report.txt
echo Missing Files: %MISSING_FILES% >> diagnostic_report.txt
echo. >> diagnostic_report.txt

echo.
echo =============================================
echo           Diagnostic Summary
echo =============================================
if %MISSING_FILES% EQU 0 (
    echo Status: ✅ ALL CHECKS PASSED
    echo The application should work correctly.
) else (
    echo Status: ❌ ISSUES FOUND
    echo Missing files: %MISSING_FILES%
    echo Please reinstall the application.
)

echo.
echo Report saved to: diagnostic_report.txt
echo.
echo Press any key to exit...
pause >nul
