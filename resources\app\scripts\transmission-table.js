// إدارة جدول الإرسال
class TransmissionTableManager {
    constructor() {
        this.transmissionData = [];
        this.filteredData = [];
        this.loadData();
        this.initializeEventListeners();
    }

    // تحميل البيانات
    loadData() {
        try {
            const savedData = localStorage.getItem('transmissionTableData');
            if (savedData) {
                this.transmissionData = JSON.parse(savedData);
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات جدول الإرسال:', error);
            this.transmissionData = [];
        }
    }

    // حفظ البيانات
    saveData() {
        try {
            localStorage.setItem('transmissionTableData', JSON.stringify(this.transmissionData));
            // حفظ في البيانات الرئيسية أيضاً
            if (typeof appData !== 'undefined') {
                appData.transmissionTable = this.transmissionData;
                saveData();
            }
        } catch (error) {
            console.error('خطأ في حفظ بيانات جدول الإرسال:', error);
        }
    }

    // إضافة عملية جديدة
    addEntry(entryData) {
        const newEntry = {
            id: Date.now().toString(),
            type: entryData.type,
            tankNumber: entryData.tankNumber || '',
            carType: entryData.carType || '',
            serialNumber: entryData.serialNumber || '',
            registrationNumber: entryData.registrationNumber,
            ownerName: entryData.ownerName,
            phoneNumber: entryData.phoneNumber || '',
            operationDate: entryData.operationDate,
            status: entryData.status || 'مكتمل',
            source: entryData.source || 'manual', // manual, certificate, card_renewal
            sourceId: entryData.sourceId || null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.transmissionData.push(newEntry);
        this.saveData();
        this.updateTable();
        this.updateSummary();
        
        return newEntry;
    }

    // تحديث عملية
    updateEntry(id, entryData) {
        const index = this.transmissionData.findIndex(entry => entry.id === id);
        if (index !== -1) {
            this.transmissionData[index] = {
                ...this.transmissionData[index],
                ...entryData,
                updatedAt: new Date().toISOString()
            };
            this.saveData();
            this.updateTable();
            this.updateSummary();
            return true;
        }
        return false;
    }

    // حذف عملية
    deleteEntry(id) {
        const index = this.transmissionData.findIndex(entry => entry.id === id);
        if (index !== -1) {
            this.transmissionData.splice(index, 1);
            this.saveData();
            this.updateTable();
            this.updateSummary();
            return true;
        }
        return false;
    }

    // استيراد من الشهادات
    importFromCertificates() {
        if (typeof appData === 'undefined') {
            showToast('لا يمكن الوصول إلى بيانات الشهادات', false);
            return;
        }

        let importedCount = 0;
        const today = new Date();
        const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

        // استيراد من شهادات التركيب
        if (appData.installationCertificates) {
            appData.installationCertificates.forEach(cert => {
                const certDate = new Date(cert.installationDate);
                if (certDate >= thirtyDaysAgo) {
                    // التحقق من عدم وجود العملية مسبقاً
                    const exists = this.transmissionData.some(entry => 
                        entry.sourceId === cert.id && entry.source === 'installation_certificate'
                    );

                    if (!exists) {
                        this.addEntry({
                            type: 'تركيب',
                            tankNumber: cert.tankNumber || '',
                            carType: cert.vehicleType || '',
                            serialNumber: cert.serialNumber || '',
                            registrationNumber: cert.vehicleNumber,
                            ownerName: cert.customerName,
                            phoneNumber: cert.phoneNumber || '',
                            operationDate: cert.installationDate,
                            status: 'مكتمل',
                            source: 'installation_certificate',
                            sourceId: cert.id
                        });
                        importedCount++;
                    }
                }
            });
        }

        // استيراد من شهادات المراقبة الدورية
        if (appData.monitoringCertificates) {
            appData.monitoringCertificates.forEach(cert => {
                const certDate = new Date(cert.lastMonitoringDate);
                if (certDate >= thirtyDaysAgo) {
                    const exists = this.transmissionData.some(entry => 
                        entry.sourceId === cert.id && entry.source === 'monitoring_certificate'
                    );

                    if (!exists) {
                        this.addEntry({
                            type: 'مراقبة',
                            tankNumber: cert.tankNumber || '',
                            carType: cert.vehicleType || '',
                            serialNumber: cert.serialNumber || '',
                            registrationNumber: cert.vehicleNumber,
                            ownerName: cert.customerName,
                            phoneNumber: cert.phoneNumber || '',
                            operationDate: cert.lastMonitoringDate,
                            status: 'مكتمل',
                            source: 'monitoring_certificate',
                            sourceId: cert.id
                        });
                        importedCount++;
                    }
                }
            });
        }

        // استيراد من تجديد البطاقات
        if (appData.gasCards) {
            appData.gasCards.forEach(card => {
                const renewalDate = new Date(card.issueDate);
                if (renewalDate >= thirtyDaysAgo) {
                    const exists = this.transmissionData.some(entry => 
                        entry.sourceId === card.id && entry.source === 'card_renewal'
                    );

                    if (!exists) {
                        // البحث عن بيانات الزبون
                        const customer = appData.customers.find(c => c.id === card.customerId);
                        const vehicle = appData.vehicles.find(v => v.id === card.vehicleId);

                        this.addEntry({
                            type: 'تجديد',
                            tankNumber: card.tankNumber || '',
                            carType: vehicle?.type || '',
                            serialNumber: card.serialNumber || '',
                            registrationNumber: card.vehicleNumber,
                            ownerName: customer?.name || card.customerName,
                            phoneNumber: customer?.phone || '',
                            operationDate: card.issueDate,
                            status: 'مكتمل',
                            source: 'card_renewal',
                            sourceId: card.id
                        });
                        importedCount++;
                    }
                }
            });
        }

        if (importedCount > 0) {
            showToast(`تم استيراد ${importedCount} عملية من الشهادات والبطاقات`, true);
        } else {
            showToast('لا توجد عمليات جديدة للاستيراد', false);
        }
    }

    // تطبيق الفلاتر
    applyFilters() {
        let filtered = [...this.transmissionData];

        // فلتر النوع
        const typeFilter = document.getElementById('transmission-type-filter')?.value;
        if (typeFilter && typeFilter !== 'all') {
            filtered = filtered.filter(entry => entry.type === typeFilter);
        }

        // فلتر الشهر
        const monthFilter = document.getElementById('transmission-month-filter')?.value;
        if (monthFilter && monthFilter !== 'all') {
            const now = new Date();
            const currentMonth = now.getMonth();
            const currentYear = now.getFullYear();

            if (monthFilter === 'current') {
                filtered = filtered.filter(entry => {
                    const entryDate = new Date(entry.operationDate);
                    return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear;
                });
            } else if (monthFilter === 'last') {
                const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
                const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;
                filtered = filtered.filter(entry => {
                    const entryDate = new Date(entry.operationDate);
                    return entryDate.getMonth() === lastMonth && entryDate.getFullYear() === lastMonthYear;
                });
            }
        }

        // فلتر التاريخ
        const dateFrom = document.getElementById('transmission-date-from')?.value;
        const dateTo = document.getElementById('transmission-date-to')?.value;
        
        if (dateFrom) {
            filtered = filtered.filter(entry => entry.operationDate >= dateFrom);
        }
        if (dateTo) {
            filtered = filtered.filter(entry => entry.operationDate <= dateTo);
        }

        // فلتر البحث
        const searchTerm = document.getElementById('search-transmission')?.value?.toLowerCase();
        if (searchTerm) {
            filtered = filtered.filter(entry =>
                entry.ownerName.toLowerCase().includes(searchTerm) ||
                entry.registrationNumber.toLowerCase().includes(searchTerm) ||
                entry.tankNumber.toLowerCase().includes(searchTerm) ||
                entry.carType.toLowerCase().includes(searchTerm) ||
                entry.phoneNumber.toLowerCase().includes(searchTerm)
            );
        }

        this.filteredData = filtered;
        return filtered;
    }

    // تحديث الجدول
    updateTable() {
        const tableBody = document.querySelector('#transmission-table-main tbody');
        if (!tableBody) return;

        const filteredData = this.applyFilters();
        
        if (filteredData.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="10" style="text-align: center; padding: 2rem; color: #6b7280;">
                        <i class="fas fa-table" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <br>لا توجد عمليات مطابقة للفلاتر المحددة
                    </td>
                </tr>
            `;
            return;
        }

        // ترتيب البيانات حسب تاريخ العملية (الأحدث أولاً)
        filteredData.sort((a, b) => new Date(b.operationDate) - new Date(a.operationDate));

        tableBody.innerHTML = filteredData.map((entry, index) => `
            <tr>
                <td>
                    <span class="operation-type ${entry.type}">${entry.type}</span>
                </td>
                <td>${entry.tankNumber || '-'}</td>
                <td>${entry.carType || '-'}</td>
                <td>${entry.serialNumber || '-'}</td>
                <td><strong>${entry.registrationNumber}</strong></td>
                <td>${entry.ownerName}</td>
                <td>${entry.phoneNumber || '-'}</td>
                <td>${this.formatDate(entry.operationDate)}</td>
                <td>
                    <span class="status ${entry.status}">${entry.status}</span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button type="button" class="btn btn-sm" onclick="transmissionManager.editEntry('${entry.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm danger" onclick="transmissionManager.confirmDeleteEntry('${entry.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    // تحديث الملخص
    updateSummary() {
        const totalCount = this.transmissionData.length;
        const installationCount = this.transmissionData.filter(entry => entry.type === 'تركيب').length;
        const monitoringCount = this.transmissionData.filter(entry => entry.type === 'مراقبة').length;
        const renewalCount = this.transmissionData.filter(entry => entry.type === 'تجديد').length;

        // عمليات الشهر الحالي
        const now = new Date();
        const currentMonth = now.getMonth();
        const currentYear = now.getFullYear();
        const currentMonthCount = this.transmissionData.filter(entry => {
            const entryDate = new Date(entry.operationDate);
            return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear;
        }).length;

        // تحديث العناصر
        const updateElement = (id, value) => {
            const element = document.getElementById(id);
            if (element) element.textContent = value;
        };

        updateElement('transmission-total-count', totalCount);
        updateElement('transmission-installation-count', installationCount);
        updateElement('transmission-monitoring-count', monitoringCount);
        updateElement('transmission-renewal-count', renewalCount);
        updateElement('transmission-current-month-count', currentMonthCount);
    }

    // تنسيق التاريخ
    formatDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    }

    // تأكيد حذف عملية
    confirmDeleteEntry(id) {
        if (confirm('هل أنت متأكد من حذف هذه العملية؟')) {
            if (this.deleteEntry(id)) {
                showToast('تم حذف العملية بنجاح', true);
            } else {
                showToast('حدث خطأ أثناء حذف العملية', false);
            }
        }
    }

    // مسح الجدول
    clearTable() {
        if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            if (confirm('تأكيد أخير: سيتم حذف جميع العمليات نهائياً. هل تريد المتابعة؟')) {
                this.transmissionData = [];
                this.saveData();
                this.updateTable();
                this.updateSummary();
                showToast('تم مسح جدول الإرسال بنجاح', true);
            }
        }
    }

    // تهيئة مستمعي الأحداث
    initializeEventListeners() {
        // سيتم إضافة المستمعين عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            this.setupEventListeners();
        });
    }

    setupEventListeners() {
        // أزرار التحكم
        const addBtn = document.getElementById('add-transmission-entry-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.showAddEntryModal());
        }

        const importBtn = document.getElementById('import-from-certificates-btn');
        if (importBtn) {
            importBtn.addEventListener('click', () => this.importFromCertificates());
        }

        const printBtn = document.getElementById('print-transmission-btn');
        if (printBtn) {
            printBtn.addEventListener('click', () => this.printTable());
        }

        const exportBtn = document.getElementById('export-transmission-pdf');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportToPDF());
        }

        const clearBtn = document.getElementById('clear-transmission-table-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearTable());
        }

        // فلاتر
        const typeFilter = document.getElementById('transmission-type-filter');
        if (typeFilter) {
            typeFilter.addEventListener('change', () => this.updateTable());
        }

        const monthFilter = document.getElementById('transmission-month-filter');
        if (monthFilter) {
            monthFilter.addEventListener('change', () => this.updateTable());
        }

        const dateFromFilter = document.getElementById('transmission-date-from');
        if (dateFromFilter) {
            dateFromFilter.addEventListener('change', () => this.updateTable());
        }

        const dateToFilter = document.getElementById('transmission-date-to');
        if (dateToFilter) {
            dateToFilter.addEventListener('change', () => this.updateTable());
        }

        // البحث
        const searchInput = document.getElementById('search-transmission');
        if (searchInput) {
            searchInput.addEventListener('input', () => this.updateTable());
        }
    }

    // عرض نافذة إضافة عملية
    showAddEntryModal() {
        // سيتم تنفيذ هذه الوظيفة في الملف الرئيسي
        if (typeof showTransmissionEntryModal === 'function') {
            showTransmissionEntryModal();
        }
    }

    // طباعة الجدول
    printTable() {
        // فتح صفحة جدول الإرسال المنفصلة للطباعة
        window.open('transmission-table.html', '_blank');
    }

    // تصدير إلى PDF
    exportToPDF() {
        // سيتم تنفيذ هذه الوظيفة لاحقاً
        showToast('ميزة تصدير PDF قيد التطوير', false);
    }
}

// إنشاء مثيل من مدير جدول الإرسال
const transmissionManager = new TransmissionTableManager();
