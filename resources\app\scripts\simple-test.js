// اختبار بسيط للنظام
// Simple System Test

(function() {
    'use strict';

    // انتظار تحميل النظام
    function waitForSystem() {
        return new Promise((resolve) => {
            const checkSystem = () => {
                if (document.readyState === 'complete' && 
                    typeof window.appData !== 'undefined') {
                    resolve();
                } else {
                    setTimeout(checkSystem, 500);
                }
            };
            checkSystem();
        });
    }

    // تشغيل اختبار بسيط
    async function runSimpleTest() {
        await waitForSystem();

        console.log('🧪 تشغيل اختبار بسيط للنظام...');

        const tests = [
            // اختبار البيانات الأساسية
            {
                name: 'وجود appData',
                test: () => typeof window.appData !== 'undefined'
            },
            {
                name: 'وجود مصفوفات البيانات',
                test: () => window.appData.customers && 
                           window.appData.vehicles && 
                           window.appData.gasTanks && 
                           window.appData.gasCards &&
                           window.appData.transmissionTable
            },
            // اختبار نظام الترحيل
            {
                name: 'وجود نظام الترحيل',
                test: () => typeof window.migrationFix !== 'undefined'
            },
            {
                name: 'وجود أدوات الترحيل',
                test: () => typeof window.migrationTools !== 'undefined'
            },
            {
                name: 'وجود وظائف وحدة التحكم',
                test: () => typeof window.resetMigration === 'function' &&
                           typeof window.startMigration === 'function' &&
                           typeof window.createTestData === 'function'
            },
            // اختبار نظام العمليات
            {
                name: 'وجود نموذج الزبون',
                test: () => document.getElementById('customer-form') !== null
            },
            {
                name: 'وجود حقل نوع العملية',
                test: () => document.getElementById('operation-type') !== null
            },
            // اختبار وظائف التحديث
            {
                name: 'وجود وظائف تحديث الجداول',
                test: () => typeof window.updateTransmissionTable === 'function' &&
                           typeof window.updateGasCardsTable === 'function'
            }
        ];

        let passed = 0;
        let failed = 0;

        tests.forEach(test => {
            try {
                if (test.test()) {
                    console.log(`✅ ${test.name}`);
                    passed++;
                } else {
                    console.log(`❌ ${test.name}`);
                    failed++;
                }
            } catch (error) {
                console.log(`💥 ${test.name}: ${error.message}`);
                failed++;
            }
        });

        const total = passed + failed;
        const percentage = ((passed / total) * 100).toFixed(1);

        console.log('\n📊 نتائج الاختبار البسيط:');
        console.log(`✅ نجح: ${passed}/${total} (${percentage}%)`);
        console.log(`❌ فشل: ${failed}/${total}`);

        if (failed === 0) {
            console.log('🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام');
        } else {
            console.log('⚠️ بعض الاختبارات فشلت، تحقق من الأخطاء أعلاه');
        }

        return { passed, failed, total, percentage };
    }

    // اختبار سريع للعمليات
    function quickOperationTest() {
        console.log('⚡ اختبار سريع للعمليات...');

        const operationTypeSelect = document.getElementById('operation-type');
        if (operationTypeSelect) {
            const options = operationTypeSelect.options;
            const hasInstallation = Array.from(options).some(opt => opt.value === 'تركيب');
            const hasMonitoring = Array.from(options).some(opt => opt.value === 'مراقبة');
            const hasRenewal = Array.from(options).some(opt => opt.value === 'تجديد');

            console.log('📋 أنواع العمليات المتاحة:');
            console.log(`- تركيب: ${hasInstallation ? '✅' : '❌'}`);
            console.log(`- مراقبة: ${hasMonitoring ? '✅' : '❌'}`);
            console.log(`- تجديد: ${hasRenewal ? '✅' : '❌'}`);

            return hasInstallation && hasMonitoring && hasRenewal;
        } else {
            console.log('❌ حقل نوع العملية غير موجود');
            return false;
        }
    }

    // اختبار البيانات
    function testDataStructure() {
        console.log('📊 فحص هيكل البيانات...');

        if (!window.appData) {
            console.log('❌ appData غير موجود');
            return false;
        }

        const requiredArrays = ['customers', 'vehicles', 'gasTanks', 'gasCards', 'transmissionTable'];
        let allGood = true;

        requiredArrays.forEach(arrayName => {
            if (Array.isArray(window.appData[arrayName])) {
                console.log(`✅ ${arrayName}: ${window.appData[arrayName].length} عنصر`);
            } else {
                console.log(`❌ ${arrayName}: غير موجود أو ليس مصفوفة`);
                allGood = false;
            }
        });

        return allGood;
    }

    // إضافة وظائف للوحة التحكم
    window.runSimpleTest = runSimpleTest;
    window.quickOperationTest = quickOperationTest;
    window.testDataStructure = testDataStructure;

    // تشغيل اختبار تلقائي عند التحميل
    if (typeof window !== 'undefined') {
        window.addEventListener('load', () => {
            setTimeout(async () => {
                try {
                    await runSimpleTest();
                    quickOperationTest();
                    testDataStructure();
                } catch (error) {
                    console.error('❌ خطأ في الاختبار التلقائي:', error);
                }
            }, 3000);
        });
    }

    console.log('✅ تم تحميل نظام الاختبار البسيط');
    console.log('💡 استخدم runSimpleTest() لتشغيل اختبار شامل');

})();
