# دليل استخدام جدول الإرسال
## Transmission Table User Guide

### 📋 نظرة عامة

جدول الإرسال هو نظام متكامل لتتبع وإدارة عمليات التركيب والمراقبة الدورية لأنظمة الغاز في المركبات. يتم ربطه تلقائياً مع نظام إدارة الزبائن وبطاقات الغاز.

### 🎯 أنواع العمليات المدعومة

#### 1. **تركيب (Installation)**
- تسجيل عمليات تركيب أنظمة الغاز الجديدة
- يتم إضافتها تلقائياً عند اختيار "تركيب" في نموذج الزبون
- تظهر في جدول الإرسال فوراً

#### 2. **مراقبة دورية (Periodic Monitoring)**
- تسجيل عمليات الفحص والصيانة الدورية
- يتم إضافتها تلقائياً عند اختيار "مراقبة" في نموذج الزبون
- ضرورية للامتثال للوائح السلامة

#### 3. **تجديد البطاقة (Card Renewal)**
- يتم حفظها في قسم "إدارة بطاقات الغاز"
- لا تظهر في جدول الإرسال
- تدار بشكل منفصل في نظام البطاقات

### 🚀 كيفية الاستخدام

#### إضافة عملية جديدة:

1. **من نموذج الزبون:**
   ```
   قسم الزبائن → إضافة زبون جديد → 
   ملء البيانات → اختيار نوع العملية → 
   حفظ (سيتم إضافة العملية تلقائياً)
   ```

2. **من جدول الإرسال مباشرة:**
   ```
   قسم جدول الإرسال → إضافة عملية جديدة → 
   ملء البيانات → حفظ
   ```

#### عرض وفلترة العمليات:

1. **الفلاتر المتاحة:**
   - نوع العملية (تركيب/مراقبة)
   - الشهر (الحالي/الماضي/الكل)
   - نطاق تاريخ محدد
   - البحث النصي

2. **البحث:**
   - اسم الزبون
   - رقم التسجيل
   - رقم الخزان
   - نوع السيارة
   - رقم الهاتف

### 📊 الإحصائيات والتقارير

#### ملخص العمليات:
- إجمالي العمليات
- عمليات التركيب
- عمليات المراقبة
- عمليات الشهر الحالي

#### التصدير والطباعة:
- طباعة التقرير الرسمي
- تصدير PDF (قيد التطوير)
- استيراد من الشهادات الموجودة

### 🔄 التكامل التلقائي

#### مع نظام الزبائن:
```
إضافة زبون + اختيار "تركيب" → جدول الإرسال
إضافة زبون + اختيار "مراقبة" → جدول الإرسال
إضافة زبون + اختيار "تجديد" → إدارة بطاقات الغاز
```

#### مع الشهادات:
- استيراد تلقائي من شهادات التركيب
- استيراد تلقائي من شهادات المراقبة
- تجنب التكرار بنظام المعرفات الفريدة

### 📝 البيانات المطلوبة

#### للتركيب والمراقبة:
- نوع العملية (إجباري)
- اسم الزبون (إجباري)
- رقم التسجيل (إجباري)
- تاريخ العملية (إجباري)
- رقم الهاتف (اختياري)
- نوع السيارة (اختياري)
- رقم خزان الغاز (اختياري)
- الرقم التسلسلي (اختياري)
- ملاحظات (اختياري)

### 🛠️ إدارة البيانات

#### العمليات المتاحة:
- ✅ إضافة عملية جديدة
- ✏️ تعديل عملية موجودة
- 🗑️ حذف عملية (مع تأكيد)
- 📥 استيراد من الشهادات
- 🧹 مسح الجدول (مع تأكيد مضاعف)

#### النسخ الاحتياطية:
- حفظ تلقائي في localStorage
- تزامن مع البيانات الرئيسية
- إمكانية الاستعادة من النسخ الاحتياطية

### 📋 التقرير الرسمي

#### محتويات التقرير:
```
الجمهورية الجزائرية الديمقراطية الشعبية
مركز وقود المستقبل - عزيري عبد الله اسحاق
رقم: 463/2019
إلى السيد: مدير الصناعة و المناجم لولاية المدية

جدول إرسال
تجدون طي هذه المراسلة الوثائق الخاصة بالسيارات 
المجهزة بغاز البترول المميع شهر [الشهر الحالي].
```

#### أعمدة الجدول:
1. تركيب أو مراقبة
2. رقم خزان الغاز
3. الصنف
4. الرقم التسلسلي
5. رقم التسجيل
6. الإسم واللقب
7. الرقم (تسلسلي)
8. تاريخ العملية

### 🎨 الواجهة والتصميم

#### ألوان العمليات:
- 🟢 **تركيب:** تدرج أخضر-فيروزي
- 🔵 **مراقبة:** تدرج أزرق-بنفسجي

#### الوضع المظلم:
- دعم كامل للوضع المظلم
- ألوان متناسقة ومريحة للعين
- تبديل تلقائي مع إعدادات التطبيق

### 🔧 استكشاف الأخطاء

#### مشاكل شائعة:

**لا تظهر العمليات:**
- تحقق من الفلاتر المطبقة
- تأكد من وجود بيانات في النظام
- جرب إعادة تحميل الصفحة

**لا يتم الحفظ التلقائي:**
- تحقق من صحة البيانات المدخلة
- تأكد من اختيار نوع العملية
- فحص وحدة التحكم للأخطاء

**مشاكل الطباعة:**
- استخدم متصفح حديث
- تأكد من إعدادات الطابعة
- جرب تصدير PDF كبديل

### 📞 الدعم الفني

للحصول على المساعدة:
- البريد الإلكتروني: <EMAIL>
- الموقع: https://github.com/future-fuel/gas-shop-management
- ساعات الدعم: 8:00 ص - 6:00 م

---

**© 2024 مؤسسة وقود المستقبل. جميع الحقوق محفوظة.**
