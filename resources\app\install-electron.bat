@echo off
chcp 65001 >nul
title تثبيت وتحديث Electron - مؤسسة وقود المستقبل

echo ========================================
echo    تثبيت وتحديث Electron
echo    مؤسسة وقود المستقبل
echo ========================================
echo.

REM Check if npm is available
echo [1/5] فحص توفر npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: npm غير متوفر أو غير موجود في PATH
    echo يرجى التأكد من تثبيت Node.js بشكل صحيح
    echo.
    echo يمكنك تنزيل Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ npm متوفر
echo المجلد الحالي: %cd%
echo.

echo [2/5] تثبيت Electron (أحدث إصدار)...
npm install electron --save-dev
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت Electron
    pause
    exit /b 1
)
echo ✅ تم تثبيت Electron بنجاح

echo.
echo [3/5] تثبيت/تحديث التبعيات الأخرى...
npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت التبعيات
    pause
    exit /b 1
)
echo ✅ تم تثبيت جميع التبعيات بنجاح

echo.
echo [4/5] فحص التثبيت...
npm list electron --depth=0
echo.

echo [5/5] اكتمل التثبيت! ✅
echo.
echo ========================================
echo يمكنك الآن تشغيل التطبيق باستخدام:
echo   npm start
echo.
echo أو بناء التطبيق باستخدام:
echo   npm run build
echo.
echo أو تشغيل في وضع التطوير:
echo   npm run dev
echo ========================================
echo.
pause
