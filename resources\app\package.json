{"name": "gas-shop-management", "productName": "مؤسسة وقود المستقبل", "version": "2.2.0", "description": "نظام إدارة مؤسسة وقود المستقبل - Future Fuel Corporation Management System", "main": "main.js", "homepage": "https://github.com/future-fuel/gas-shop-management", "author": {"name": "Future Fuel Corporation", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"start": "electron main.js", "dev": "electron main.js --dev", "web": "start index.html", "electron": "electron main.js", "build": "electron-builder", "build-win": "electron-builder --win", "build-win32": "electron-builder --win --ia32", "build-win64": "electron-builder --win --x64", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "postinstall": "npm install --production", "install-deps": "npm install express cors compression helmet open nodemon"}, "keywords": ["gas", "shop", "management", "arabic", "fuel", "certificates", "customers", "inventory"], "build": {"appId": "com.futurefuel.gasshop", "productName": "مؤسسة وقود المستقبل", "copyright": "Copyright © 2024 Future Fuel Corporation", "directories": {"output": "build-output"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}", "!dist/", "!build/", "!*.bat", "!*.vbs", "!*.ps1", "!*.nsh"], "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}", "sign": null}, "nsis": {"oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "icons/app-icon.ico", "uninstallerIcon": "icons/app-icon.ico", "installerHeaderIcon": "icons/app-icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "مؤسسة وقود المستقبل", "include": "installer.nsh", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Business", "artifactName": "${productName}-Setup-${version}-${arch}.${ext}", "displayLanguageSelector": false, "language": "ar", "warningsAsErrors": false, "unicode": true, "guid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "differentialPackage": true}, "portable": {"artifactName": "${productName}-Portable-${version}-${arch}.${ext}"}, "compression": "maximum", "npmRebuild": false, "nodeGypRebuild": false, "afterSign": null, "afterAllArtifactBuild": null}, "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "electron-log": "^5.0.1", "electron-updater": "^6.1.7", "express": "^4.18.2", "fs-extra": "^11.1.1", "helmet": "^7.1.0", "open": "^9.1.0"}, "devDependencies": {"electron": "^32.3.3", "electron-builder": "^25.1.8", "electron-packager": "^17.1.2", "nodemon": "^3.0.2"}, "electronDownload": {"mirror": "https://github.com/electron/electron/releases/download/"}}