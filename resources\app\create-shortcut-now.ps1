# Create Desktop Shortcut
Write-Host "Creating desktop shortcut..." -ForegroundColor Yellow

try {
    # Get desktop path
    $DesktopPath = [Environment]::GetFolderPath("Desktop")
    $CurrentPath = Get-Location

    Write-Host "Desktop path: $DesktopPath" -ForegroundColor Blue
    Write-Host "App path: $CurrentPath" -ForegroundColor Blue

    # Create Shell object
    $WshShell = New-Object -ComObject WScript.Shell

    # Shortcut path with Arabic name
    $ShortcutPath = "$DesktopPath\Future Fuel Corporation.lnk"

    # Create shortcut
    $Shortcut = $WshShell.CreateShortcut($ShortcutPath)
    $Shortcut.TargetPath = "$CurrentPath\run-app.bat"
    $Shortcut.WorkingDirectory = "$CurrentPath"
    $Shortcut.Description = "Future Fuel Corporation Management System"
    
    # Add icon if exists
    $IconPath = "$CurrentPath\icons\app-icon.ico"
    if (Test-Path $IconPath) {
        $Shortcut.IconLocation = $IconPath
        Write-Host "Icon found: $IconPath" -ForegroundColor Green
    } else {
        Write-Host "Icon not found, using default icon" -ForegroundColor Yellow
    }

    # Save shortcut
    $Shortcut.Save()

    Write-Host "Desktop shortcut created successfully!" -ForegroundColor Green
    Write-Host "Shortcut location: $ShortcutPath" -ForegroundColor Cyan

    # Verify shortcut exists
    if (Test-Path $ShortcutPath) {
        Write-Host "Shortcut verified!" -ForegroundColor Green

        # Create start menu shortcut too
        $StartMenuPath = [Environment]::GetFolderPath("StartMenu") + "\Programs"
        $StartMenuShortcut = "$StartMenuPath\Future Fuel Corporation.lnk"

        $StartShortcut = $WshShell.CreateShortcut($StartMenuShortcut)
        $StartShortcut.TargetPath = "$CurrentPath\run-app.bat"
        $StartShortcut.WorkingDirectory = "$CurrentPath"
        $StartShortcut.Description = "Future Fuel Corporation Management System"
        if (Test-Path $IconPath) {
            $StartShortcut.IconLocation = $IconPath
        }
        $StartShortcut.Save()

        Write-Host "Start menu shortcut created too!" -ForegroundColor Green

    } else {
        Write-Host "Failed to create shortcut" -ForegroundColor Red
    }

} catch {
    Write-Host "Error creating shortcut: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Installation completed!" -ForegroundColor Green
Write-Host "You can now double-click the 'Future Fuel Corporation' icon on desktop" -ForegroundColor Cyan
