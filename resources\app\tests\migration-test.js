// اختبار نظام ترحيل البيانات
// Data Migration System Test

class MigrationTest {
    constructor() {
        this.testResults = [];
        this.totalTests = 0;
        this.passedTests = 0;
        this.failedTests = 0;
    }

    /**
     * تشغيل جميع اختبارات نظام الترحيل
     */
    async runMigrationTests() {
        console.log('🧪 بدء اختبارات نظام ترحيل البيانات...');
        
        try {
            // اختبار تهيئة النظام
            await this.testSystemInitialization();
            
            // اختبار فحص البيانات القديمة
            await this.testOldDataDetection();
            
            // اختبار إنشاء بيانات تجريبية
            await this.testCreateTestData();
            
            // اختبار عملية الترحيل
            await this.testMigrationProcess();
            
            // اختبار التحقق من النتائج
            await this.testMigrationResults();
            
            // اختبار أدوات الإدارة
            await this.testManagementTools();
            
            // عرض النتائج
            this.showTestResults();
            
        } catch (error) {
            console.error('❌ خطأ في تشغيل اختبارات نظام الترحيل:', error);
        }
    }

    /**
     * اختبار تهيئة النظام
     */
    async testSystemInitialization() {
        console.log('🔧 اختبار تهيئة نظام الترحيل...');
        
        // اختبار وجود نظام الترحيل المحسن
        this.test('وجود نظام الترحيل المحسن', () => {
            return typeof window.migrationFix !== 'undefined';
        });

        // اختبار وجود أدوات الترحيل
        this.test('وجود أدوات الترحيل', () => {
            return typeof window.migrationTools !== 'undefined';
        });

        // اختبار تهيئة أدوات الترحيل
        this.test('تهيئة أدوات الترحيل', () => {
            return window.migrationTools && window.migrationTools.isInitialized;
        });

        // اختبار وجود وظائف وحدة التحكم
        this.test('وجود وظائف وحدة التحكم', () => {
            return typeof window.resetMigration === 'function' &&
                   typeof window.startMigration === 'function' &&
                   typeof window.checkMigrationStatus === 'function' &&
                   typeof window.checkOldData === 'function';
        });

        // اختبار وجود زر إدارة الترحيل
        this.test('وجود زر إدارة الترحيل', () => {
            return document.getElementById('migration-manager-btn') !== null;
        });
    }

    /**
     * اختبار فحص البيانات القديمة
     */
    async testOldDataDetection() {
        console.log('📋 اختبار فحص البيانات القديمة...');
        
        // اختبار وظيفة فحص البيانات القديمة
        this.test('وظيفة فحص البيانات القديمة', () => {
            if (window.migrationFix) {
                const hasOldData = window.migrationFix.checkForOldData();
                return typeof hasOldData === 'boolean';
            }
            return false;
        });

        // اختبار فحص localStorage
        this.test('فحص localStorage للبيانات القديمة', () => {
            const gasShopData = localStorage.getItem('gasShopData');
            const customersData = localStorage.getItem('customersData');
            
            // إذا كانت هناك بيانات، فالاختبار ناجح
            // إذا لم تكن هناك بيانات، فهذا أيضاً صحيح
            return true;
        });

        // اختبار فحص appData
        this.test('فحص appData للبيانات', () => {
            return window.appData !== undefined;
        });
    }

    /**
     * اختبار إنشاء بيانات تجريبية
     */
    async testCreateTestData() {
        console.log('🧪 اختبار إنشاء بيانات تجريبية...');
        
        // إنشاء بيانات تجريبية
        this.test('إنشاء بيانات تجريبية', () => {
            try {
                if (typeof window.createTestData === 'function') {
                    const testData = window.createTestData();
                    return testData && testData.customers && testData.customers.length > 0;
                }
                return false;
            } catch (error) {
                console.error('خطأ في إنشاء البيانات التجريبية:', error);
                return false;
            }
        });

        // التحقق من حفظ البيانات التجريبية
        this.test('حفظ البيانات التجريبية في localStorage', () => {
            const gasShopData = localStorage.getItem('gasShopData');
            return gasShopData !== null;
        });

        // التحقق من صحة البيانات التجريبية
        this.test('صحة البيانات التجريبية', () => {
            try {
                const gasShopData = localStorage.getItem('gasShopData');
                if (gasShopData) {
                    const data = JSON.parse(gasShopData);
                    return data.customers && 
                           data.vehicles && 
                           data.gasTanks && 
                           data.gasCards &&
                           data.transmissionTable;
                }
                return false;
            } catch (error) {
                return false;
            }
        });
    }

    /**
     * اختبار عملية الترحيل
     */
    async testMigrationProcess() {
        console.log('🚀 اختبار عملية الترحيل...');
        
        // إعادة تعيين حالة الترحيل
        this.test('إعادة تعيين حالة الترحيل', () => {
            try {
                if (typeof window.resetMigration === 'function') {
                    window.resetMigration();
                    const completed = localStorage.getItem('dataMigrationCompleted');
                    return completed === null;
                }
                return false;
            } catch (error) {
                return false;
            }
        });

        // اختبار بدء عملية الترحيل
        this.test('بدء عملية الترحيل', async () => {
            try {
                if (typeof window.startMigration === 'function') {
                    const result = await window.startMigration();
                    return result === true;
                }
                return false;
            } catch (error) {
                console.error('خطأ في بدء الترحيل:', error);
                return false;
            }
        });

        // اختبار حالة الترحيل بعد الإكمال
        this.test('حالة الترحيل بعد الإكمال', () => {
            if (window.migrationFix) {
                const isCompleted = window.migrationFix.isMigrationCompleted();
                return isCompleted === true;
            }
            return false;
        });
    }

    /**
     * اختبار التحقق من النتائج
     */
    async testMigrationResults() {
        console.log('✅ اختبار نتائج الترحيل...');
        
        // اختبار وجود البيانات في appData
        this.test('وجود البيانات في appData', () => {
            return window.appData && 
                   window.appData.customers && 
                   window.appData.vehicles && 
                   window.appData.gasTanks && 
                   window.appData.gasCards &&
                   window.appData.transmissionTable;
        });

        // اختبار عدد الزبائن المرحلين
        this.test('ترحيل الزبائن', () => {
            return window.appData.customers && window.appData.customers.length > 0;
        });

        // اختبار عدد السيارات المرحلة
        this.test('ترحيل السيارات', () => {
            return window.appData.vehicles && window.appData.vehicles.length > 0;
        });

        // اختبار عدد خزانات الغاز المرحلة
        this.test('ترحيل خزانات الغاز', () => {
            return window.appData.gasTanks && window.appData.gasTanks.length > 0;
        });

        // اختبار عدد بطاقات الغاز المرحلة
        this.test('ترحيل بطاقات الغاز', () => {
            return window.appData.gasCards && window.appData.gasCards.length > 0;
        });

        // اختبار عدد عمليات الإرسال المرحلة
        this.test('ترحيل عمليات الإرسال', () => {
            return window.appData.transmissionTable && window.appData.transmissionTable.length > 0;
        });

        // اختبار صحة البيانات المرحلة
        this.test('صحة البيانات المرحلة', () => {
            try {
                // التحقق من وجود معرفات صحيحة
                const customer = window.appData.customers[0];
                const vehicle = window.appData.vehicles[0];
                const tank = window.appData.gasTanks[0];
                
                return customer && customer.id && customer.name &&
                       vehicle && vehicle.id && vehicle.plate_number &&
                       tank && tank.id && tank.serial_number;
            } catch (error) {
                return false;
            }
        });
    }

    /**
     * اختبار أدوات الإدارة
     */
    async testManagementTools() {
        console.log('🛠️ اختبار أدوات الإدارة...');
        
        // اختبار وظيفة فحص الحالة
        this.test('وظيفة فحص الحالة', () => {
            try {
                if (typeof window.checkMigrationStatus === 'function') {
                    const status = window.checkMigrationStatus();
                    return status && status.isCompleted !== undefined;
                }
                return false;
            } catch (error) {
                return false;
            }
        });

        // اختبار وظيفة فحص البيانات القديمة
        this.test('وظيفة فحص البيانات القديمة', () => {
            try {
                if (typeof window.checkOldData === 'function') {
                    const hasOldData = window.checkOldData();
                    return typeof hasOldData === 'boolean';
                }
                return false;
            } catch (error) {
                return false;
            }
        });

        // اختبار إنشاء النسخة الاحتياطية
        this.test('إنشاء النسخة الاحتياطية', () => {
            const keys = Object.keys(localStorage);
            const backupExists = keys.some(key => key.startsWith('migration_backup_'));
            return backupExists;
        });
    }

    /**
     * تشغيل اختبار واحد
     */
    test(name, testFunction) {
        this.totalTests++;
        
        try {
            let result;
            if (testFunction.constructor.name === 'AsyncFunction') {
                // للاختبارات غير المتزامنة
                result = testFunction();
            } else {
                result = testFunction();
            }
            
            if (result === true || (result && result.then)) {
                this.passedTests++;
                this.testResults.push({ name, status: 'PASS', error: null });
                console.log(`✅ ${name}`);
            } else {
                this.failedTests++;
                this.testResults.push({ name, status: 'FAIL', error: 'Test returned false' });
                console.log(`❌ ${name}`);
            }
        } catch (error) {
            this.failedTests++;
            this.testResults.push({ name, status: 'ERROR', error: error.message });
            console.log(`💥 ${name}: ${error.message}`);
        }
    }

    /**
     * عرض نتائج الاختبارات
     */
    showTestResults() {
        console.log('\n📊 نتائج اختبارات نظام الترحيل:');
        console.log('='.repeat(50));
        console.log(`إجمالي الاختبارات: ${this.totalTests}`);
        console.log(`نجح: ${this.passedTests} ✅`);
        console.log(`فشل: ${this.failedTests} ❌`);
        console.log(`معدل النجاح: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%`);
        
        if (this.failedTests > 0) {
            console.log('\n❌ الاختبارات الفاشلة:');
            this.testResults
                .filter(test => test.status !== 'PASS')
                .forEach(test => {
                    console.log(`- ${test.name}: ${test.error || 'فشل'}`);
                });
        }
        
        // عرض ملخص البيانات
        this.showDataSummary();
        
        // عرض إشعار للمستخدم
        if (typeof window.showEnhancedToast === 'function') {
            const message = `اختبارات نظام الترحيل: ${this.passedTests}/${this.totalTests} نجح`;
            const type = this.failedTests === 0 ? 'success' : 'warning';
            window.showEnhancedToast(message, type, 5000);
        }
        
        return {
            total: this.totalTests,
            passed: this.passedTests,
            failed: this.failedTests,
            results: this.testResults
        };
    }

    /**
     * عرض ملخص البيانات
     */
    showDataSummary() {
        console.log('\n📋 ملخص البيانات بعد الترحيل:');
        
        if (window.appData) {
            console.log(`- الزبائن: ${window.appData.customers?.length || 0}`);
            console.log(`- السيارات: ${window.appData.vehicles?.length || 0}`);
            console.log(`- خزانات الغاز: ${window.appData.gasTanks?.length || 0}`);
            console.log(`- بطاقات الغاز: ${window.appData.gasCards?.length || 0}`);
            console.log(`- عمليات الإرسال: ${window.appData.transmissionTable?.length || 0}`);
            console.log(`- المواعيد: ${window.appData.appointments?.length || 0}`);
            console.log(`- الديون: ${window.appData.debts?.length || 0}`);
        } else {
            console.log('- لا توجد بيانات في appData');
        }
        
        // فحص حالة الترحيل
        if (window.migrationFix) {
            const isCompleted = window.migrationFix.isMigrationCompleted();
            console.log(`- حالة الترحيل: ${isCompleted ? 'مكتمل' : 'غير مكتمل'}`);
        }
    }

    /**
     * اختبار سريع لنظام الترحيل
     */
    async quickMigrationTest() {
        console.log('⚡ اختبار سريع لنظام الترحيل...');
        
        const quickTests = [
            () => typeof window.migrationFix !== 'undefined',
            () => typeof window.migrationTools !== 'undefined',
            () => typeof window.resetMigration === 'function',
            () => typeof window.startMigration === 'function',
            () => typeof window.checkMigrationStatus === 'function'
        ];
        
        const results = quickTests.map(test => test());
        const passed = results.filter(Boolean).length;
        
        console.log(`اختبار سريع للترحيل: ${passed}/${quickTests.length} مكونات جاهزة`);
        
        return passed === quickTests.length;
    }
}

// إنشاء مثيل من نظام اختبار الترحيل
const migrationTest = new MigrationTest();

// تصدير النظام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = migrationTest;
} else {
    window.migrationTest = migrationTest;
}

// تشغيل اختبار سريع عند التحميل
if (typeof window !== 'undefined') {
    window.addEventListener('load', () => {
        setTimeout(() => {
            migrationTest.quickMigrationTest();
        }, 4000);
    });
}
