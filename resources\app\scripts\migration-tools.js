// أدوات إدارة الترحيل
// Migration Management Tools

class MigrationTools {
    constructor() {
        this.isInitialized = false;
    }

    /**
     * تهيئة أدوات الترحيل
     */
    initialize() {
        if (this.isInitialized) return;

        console.log('🔧 تهيئة أدوات إدارة الترحيل...');

        // إضافة أدوات إدارة الترحيل إلى وحدة التحكم
        this.addConsoleTools();

        // إضافة واجهة مرئية للترحيل
        this.addMigrationUI();

        this.isInitialized = true;
        console.log('✅ تم تهيئة أدوات إدارة الترحيل');
    }

    /**
     * إضافة أدوات وحدة التحكم
     */
    addConsoleTools() {
        // أداة إعادة تعيين الترحيل
        window.resetMigration = () => {
            localStorage.removeItem('dataMigrationCompleted');
            console.log('✅ تم إعادة تعيين حالة الترحيل');
            console.log('💡 يمكنك الآن إعادة تشغيل الترحيل باستخدام: startMigration()');
        };

        // أداة بدء الترحيل يدوياً
        window.startMigration = async () => {
            if (window.migrationFix) {
                console.log('🚀 بدء الترحيل اليدوي...');
                const result = await window.migrationFix.startMigration();
                if (result) {
                    console.log('✅ تم الترحيل بنجاح');
                } else {
                    console.log('❌ فشل الترحيل');
                }
                return result;
            } else {
                console.error('❌ نظام الترحيل غير متوفر');
                return false;
            }
        };

        // أداة فحص حالة الترحيل
        window.checkMigrationStatus = () => {
            if (window.migrationFix) {
                const status = window.migrationFix.getStatus();
                const isCompleted = window.migrationFix.isMigrationCompleted();
                
                console.log('📊 حالة الترحيل:');
                console.log('- مكتمل:', isCompleted);
                console.log('- قيد التشغيل:', status.isRunning);
                console.log('- التقدم:', status.progress + '%');
                console.log('- الخطوة الحالية:', status.currentStep);
                console.log('- عدد الأخطاء:', status.errors.length);
                console.log('- عدد التحذيرات:', status.warnings.length);
                
                if (status.migratedCounts) {
                    console.log('- البيانات المرحلة:', status.migratedCounts);
                }
                
                return { status, isCompleted };
            } else {
                console.error('❌ نظام الترحيل غير متوفر');
                return null;
            }
        };

        // أداة فحص البيانات القديمة
        window.checkOldData = () => {
            if (window.migrationFix) {
                const hasOldData = window.migrationFix.checkForOldData();
                console.log('📋 وجود بيانات قديمة:', hasOldData);
                
                // فحص تفصيلي
                const gasShopData = localStorage.getItem('gasShopData');
                const customersData = localStorage.getItem('customersData');
                
                console.log('- gasShopData:', gasShopData ? 'موجود' : 'غير موجود');
                console.log('- customersData:', customersData ? 'موجود' : 'غير موجود');
                console.log('- appData:', window.appData ? 'موجود' : 'غير موجود');
                
                if (window.appData) {
                    console.log('- عدد الزبائن في appData:', window.appData.customers?.length || 0);
                    console.log('- عدد السيارات في appData:', window.appData.vehicles?.length || 0);
                    console.log('- عدد بطاقات الغاز في appData:', window.appData.gasCards?.length || 0);
                }
                
                return hasOldData;
            } else {
                console.error('❌ نظام الترحيل غير متوفر');
                return false;
            }
        };

        // أداة تنظيف البيانات القديمة
        window.cleanOldData = () => {
            const confirm_clean = confirm('هل أنت متأكد من حذف جميع البيانات القديمة؟ هذا الإجراء لا يمكن التراجع عنه.');
            if (confirm_clean) {
                localStorage.removeItem('gasShopData');
                localStorage.removeItem('customersData');
                localStorage.removeItem('dataMigrationCompleted');
                
                // حذف النسخ الاحتياطية القديمة
                const keys = Object.keys(localStorage);
                keys.forEach(key => {
                    if (key.startsWith('migration_backup_')) {
                        localStorage.removeItem(key);
                    }
                });
                
                console.log('✅ تم تنظيف جميع البيانات القديمة');
                console.log('💡 يمكنك الآن إعادة تشغيل التطبيق لبدء جلسة جديدة');
            }
        };

        // أداة إنشاء بيانات تجريبية
        window.createTestData = () => {
            const testData = {
                customers: [
                    {
                        id: 'test-customer-1',
                        name: 'أحمد محمد',
                        phone: '0123456789',
                        email: '<EMAIL>',
                        address: 'الرياض، المملكة العربية السعودية',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'test-customer-2',
                        name: 'فاطمة علي',
                        phone: '0987654321',
                        email: '<EMAIL>',
                        address: 'جدة، المملكة العربية السعودية',
                        createdAt: new Date().toISOString()
                    }
                ],
                vehicles: [
                    {
                        id: 'test-vehicle-1',
                        customerId: 'test-customer-1',
                        plateNumber: 'ABC-123',
                        brand: 'تويوتا',
                        model: 'كامري',
                        year: 2020,
                        color: 'أبيض',
                        createdAt: new Date().toISOString()
                    }
                ],
                gasTanks: [
                    {
                        id: 'test-tank-1',
                        vehicleId: 'test-vehicle-1',
                        tankType: 'أسطوانة',
                        tankBrand: 'الوطنية',
                        tankSerialNumber: 'TK-2024-001',
                        tankCapacity: 60,
                        createdAt: new Date().toISOString()
                    }
                ],
                gasCards: [
                    {
                        id: 'test-card-1',
                        customerId: 'test-customer-1',
                        vehicleId: 'test-vehicle-1',
                        cardNumber: 'GC-2024-001',
                        issueDate: new Date().toISOString().split('T')[0],
                        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                        createdAt: new Date().toISOString()
                    }
                ],
                transmissionTable: [
                    {
                        id: 'test-trans-1',
                        type: 'تركيب',
                        ownerName: 'أحمد محمد',
                        phoneNumber: '0123456789',
                        registrationNumber: 'ABC-123',
                        tankNumber: 'TK-2024-001',
                        operationDate: new Date().toISOString().split('T')[0],
                        status: 'مكتمل',
                        createdAt: new Date().toISOString()
                    }
                ]
            };

            localStorage.setItem('gasShopData', JSON.stringify(testData));
            localStorage.removeItem('dataMigrationCompleted');
            
            console.log('✅ تم إنشاء بيانات تجريبية');
            console.log('💡 يمكنك الآن اختبار الترحيل باستخدام: startMigration()');
            
            return testData;
        };

        console.log('✅ تم إضافة أدوات وحدة التحكم:');
        console.log('- resetMigration() - إعادة تعيين حالة الترحيل');
        console.log('- startMigration() - بدء الترحيل يدوياً');
        console.log('- checkMigrationStatus() - فحص حالة الترحيل');
        console.log('- checkOldData() - فحص البيانات القديمة');
        console.log('- cleanOldData() - تنظيف البيانات القديمة');
        console.log('- createTestData() - إنشاء بيانات تجريبية');
    }

    /**
     * إضافة واجهة مرئية للترحيل
     */
    addMigrationUI() {
        // إنشاء زر إدارة الترحيل
        const migrationButton = document.createElement('button');
        migrationButton.id = 'migration-manager-btn';
        migrationButton.innerHTML = '🔄 إدارة الترحيل';
        migrationButton.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            z-index: 9999;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        `;

        migrationButton.addEventListener('click', () => {
            this.showMigrationDialog();
        });

        document.body.appendChild(migrationButton);

        console.log('✅ تم إضافة واجهة إدارة الترحيل');
    }

    /**
     * إظهار نافذة إدارة الترحيل
     */
    showMigrationDialog() {
        // إنشاء النافذة
        const dialog = document.createElement('div');
        dialog.id = 'migration-dialog';
        dialog.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10000;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        `;

        // محتوى النافذة
        dialog.innerHTML = `
            <h3 style="margin-top: 0; color: #333;">🔄 إدارة ترحيل البيانات</h3>
            
            <div id="migration-status" style="margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                <strong>حالة الترحيل:</strong> <span id="status-text">جاري التحقق...</span>
            </div>
            
            <div style="margin-bottom: 15px;">
                <button onclick="window.checkMigrationStatus()" style="margin: 5px; padding: 8px 12px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    📊 فحص الحالة
                </button>
                <button onclick="window.checkOldData()" style="margin: 5px; padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    📋 فحص البيانات القديمة
                </button>
            </div>
            
            <div style="margin-bottom: 15px;">
                <button onclick="window.startMigration()" style="margin: 5px; padding: 8px 12px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    🚀 بدء الترحيل
                </button>
                <button onclick="window.resetMigration()" style="margin: 5px; padding: 8px 12px; background: #ffc107; color: black; border: none; border-radius: 4px; cursor: pointer;">
                    🔄 إعادة تعيين
                </button>
            </div>
            
            <div style="margin-bottom: 15px;">
                <button onclick="window.createTestData()" style="margin: 5px; padding: 8px 12px; background: #6f42c1; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    🧪 إنشاء بيانات تجريبية
                </button>
                <button onclick="window.cleanOldData()" style="margin: 5px; padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    🗑️ تنظيف البيانات
                </button>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <button onclick="document.getElementById('migration-dialog').remove()" style="padding: 8px 20px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    إغلاق
                </button>
            </div>
        `;

        // إضافة overlay
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
        `;
        overlay.addEventListener('click', () => {
            dialog.remove();
            overlay.remove();
        });

        document.body.appendChild(overlay);
        document.body.appendChild(dialog);

        // تحديث حالة الترحيل
        this.updateDialogStatus();
    }

    /**
     * تحديث حالة الترحيل في النافذة
     */
    updateDialogStatus() {
        const statusText = document.getElementById('status-text');
        if (!statusText) return;

        if (window.migrationFix) {
            const isCompleted = window.migrationFix.isMigrationCompleted();
            const hasOldData = window.migrationFix.checkForOldData();
            
            if (isCompleted) {
                statusText.innerHTML = '<span style="color: #28a745;">✅ مكتمل</span>';
            } else if (hasOldData) {
                statusText.innerHTML = '<span style="color: #ffc107;">⚠️ يحتاج ترحيل</span>';
            } else {
                statusText.innerHTML = '<span style="color: #6c757d;">ℹ️ لا توجد بيانات للترحيل</span>';
            }
        } else {
            statusText.innerHTML = '<span style="color: #dc3545;">❌ نظام الترحيل غير متوفر</span>';
        }
    }
}

// إنشاء مثيل من أدوات الترحيل
const migrationTools = new MigrationTools();

// تصدير الأدوات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = migrationTools;
} else {
    window.migrationTools = migrationTools;
}

// تهيئة تلقائية عند تحميل الصفحة
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            migrationTools.initialize();
        }, 2000);
    });
}
