// اختبارات النظام المحسن
// Enhanced System Tests for Future Fuel Management

class SystemTester {
    constructor() {
        this.testResults = [];
        this.totalTests = 0;
        this.passedTests = 0;
        this.failedTests = 0;
    }

    /**
     * تشغيل جميع الاختبارات
     */
    async runAllTests() {
        console.log('🧪 بدء اختبارات النظام المحسن...');
        
        try {
            // اختبار تهيئة النظام
            await this.testSystemInitialization();
            
            // اختبار قاعدة البيانات
            await this.testDatabaseConnection();
            
            // اختبار مدير الزبائن
            await this.testCustomersManager();
            
            // اختبار إصلاحات الأخطاء
            await this.testBugFixes();
            
            // اختبار ترحيل البيانات
            await this.testDataMigration();
            
            // اختبار واجهة المستخدم
            await this.testUserInterface();
            
            // عرض النتائج
            this.showTestResults();
            
        } catch (error) {
            console.error('❌ خطأ في تشغيل الاختبارات:', error);
        }
    }

    /**
     * اختبار تهيئة النظام
     */
    async testSystemInitialization() {
        console.log('🔧 اختبار تهيئة النظام...');
        
        // اختبار وجود النظام المتكامل
        this.test('وجود نظام التكامل', () => {
            return typeof window.systemIntegration !== 'undefined';
        });

        // اختبار وجود مدير قاعدة البيانات
        this.test('وجود مدير قاعدة البيانات', () => {
            return typeof window.dbManager !== 'undefined';
        });

        // اختبار وجود مدير الزبائن
        this.test('وجود مدير الزبائن', () => {
            return typeof window.customersManager !== 'undefined';
        });

        // اختبار وجود نظام الإصلاحات
        this.test('وجود نظام الإصلاحات', () => {
            return typeof window.bugFixes !== 'undefined';
        });

        // اختبار وجود نظام الترحيل
        this.test('وجود نظام الترحيل', () => {
            return typeof window.dataMigration !== 'undefined';
        });
    }

    /**
     * اختبار اتصال قاعدة البيانات
     */
    async testDatabaseConnection() {
        console.log('🗄️ اختبار اتصال قاعدة البيانات...');
        
        // اختبار تهيئة قاعدة البيانات
        this.test('تهيئة قاعدة البيانات', () => {
            return window.dbManager && typeof window.dbManager.initialize === 'function';
        });

        // اختبار وظائف قاعدة البيانات الأساسية
        this.test('وظائف قاعدة البيانات الأساسية', () => {
            if (!window.dbManager) return false;
            
            const requiredMethods = [
                'addCustomer',
                'updateCustomer',
                'deleteCustomer',
                'searchCustomers',
                'getAllCustomers'
            ];
            
            return requiredMethods.every(method => 
                typeof window.dbManager[method] === 'function'
            );
        });

        // اختبار التحقق من صحة البيانات
        this.test('التحقق من صحة البيانات', () => {
            if (!window.dbManager) return false;
            
            return typeof window.dbManager.validateCustomerData === 'function';
        });
    }

    /**
     * اختبار مدير الزبائن
     */
    async testCustomersManager() {
        console.log('👥 اختبار مدير الزبائن...');
        
        // اختبار تهيئة مدير الزبائن
        this.test('تهيئة مدير الزبائن', () => {
            return window.customersManager && typeof window.customersManager.initialize === 'function';
        });

        // اختبار وظائف إدارة الزبائن
        this.test('وظائف إدارة الزبائن', () => {
            if (!window.customersManager) return false;
            
            const requiredMethods = [
                'addCustomer',
                'updateCustomer',
                'deleteCustomer',
                'searchCustomers',
                'loadCustomers'
            ];
            
            return requiredMethods.every(method => 
                typeof window.customersManager[method] === 'function'
            );
        });

        // اختبار التحقق من البيانات
        this.test('التحقق من بيانات الزبائن', () => {
            if (!window.customersManager) return false;
            
            const testData = {
                name: 'اختبار',
                phone: '0123456789',
                email: '<EMAIL>'
            };
            
            const validation = window.customersManager.validateCustomerData(testData);
            return validation && validation.isValid === true;
        });

        // اختبار البحث المحلي
        this.test('البحث المحلي', () => {
            if (!window.customersManager) return false;
            
            return typeof window.customersManager.searchCustomersLocally === 'function';
        });

        // اختبار التخزين المؤقت
        this.test('التخزين المؤقت', () => {
            if (!window.customersManager) return false;
            
            return window.customersManager.searchCache instanceof Map;
        });
    }

    /**
     * اختبار إصلاحات الأخطاء
     */
    async testBugFixes() {
        console.log('🔧 اختبار إصلاحات الأخطاء...');
        
        // اختبار وجود الوظائف المساعدة
        this.test('الوظائف المساعدة', () => {
            const utilityFunctions = [
                'generateUniqueId',
                'formatDate',
                'sanitizeText',
                'validatePhone',
                'validateEmail'
            ];
            
            return utilityFunctions.every(func => 
                typeof window[func] === 'function'
            );
        });

        // اختبار وظيفة debounce
        this.test('وظيفة debounce', () => {
            return typeof window.debounce === 'function';
        });

        // اختبار نظام التخزين المؤقت
        this.test('نظام التخزين المؤقت', () => {
            return typeof window.SimpleCache === 'function' && window.appCache instanceof window.SimpleCache;
        });

        // اختبار التحقق من البيانات المحسن
        this.test('التحقق من البيانات المحسن', () => {
            return typeof window.validateInput === 'function';
        });

        // اختبار حفظ البيانات المحسن
        this.test('حفظ البيانات المحسن', () => {
            return typeof window.saveDataDebounced === 'function';
        });
    }

    /**
     * اختبار ترحيل البيانات
     */
    async testDataMigration() {
        console.log('🔄 اختبار ترحيل البيانات...');
        
        // اختبار وجود نظام الترحيل
        this.test('وجود نظام الترحيل', () => {
            return window.dataMigration && typeof window.dataMigration.startMigration === 'function';
        });

        // اختبار وظائف الترحيل
        this.test('وظائف الترحيل', () => {
            if (!window.dataMigration) return false;
            
            const requiredMethods = [
                'loadOldData',
                'validateData',
                'transformData',
                'migrateCustomers'
            ];
            
            return requiredMethods.every(method => 
                typeof window.dataMigration[method] === 'function'
            );
        });

        // اختبار حالة الترحيل
        this.test('حالة الترحيل', () => {
            if (!window.dataMigration) return false;
            
            const status = window.dataMigration.getStatus();
            return status && typeof status === 'object';
        });
    }

    /**
     * اختبار واجهة المستخدم
     */
    async testUserInterface() {
        console.log('🎨 اختبار واجهة المستخدم...');
        
        // اختبار وجود العناصر الأساسية
        this.test('العناصر الأساسية', () => {
            const requiredElements = [
                'customers-table',
                'add-customer-btn',
                'customer-modal'
            ];
            
            return requiredElements.every(id => 
                document.getElementById(id) !== null
            );
        });

        // اختبار الإشعارات المحسنة
        this.test('الإشعارات المحسنة', () => {
            return typeof window.showEnhancedToast === 'function';
        });

        // اختبار مؤشر حالة الاتصال
        this.test('مؤشر حالة الاتصال', () => {
            return document.getElementById('connection-status') !== null;
        });

        // اختبار الأنماط المحسنة
        this.test('الأنماط المحسنة', () => {
            const styles = document.querySelector('style');
            return styles && styles.textContent.includes('toast');
        });
    }

    /**
     * تشغيل اختبار واحد
     */
    test(name, testFunction) {
        this.totalTests++;
        
        try {
            const result = testFunction();
            
            if (result) {
                this.passedTests++;
                this.testResults.push({ name, status: 'PASS', error: null });
                console.log(`✅ ${name}`);
            } else {
                this.failedTests++;
                this.testResults.push({ name, status: 'FAIL', error: 'Test returned false' });
                console.log(`❌ ${name}`);
            }
        } catch (error) {
            this.failedTests++;
            this.testResults.push({ name, status: 'ERROR', error: error.message });
            console.log(`💥 ${name}: ${error.message}`);
        }
    }

    /**
     * عرض نتائج الاختبارات
     */
    showTestResults() {
        console.log('\n📊 نتائج الاختبارات:');
        console.log('='.repeat(50));
        console.log(`إجمالي الاختبارات: ${this.totalTests}`);
        console.log(`نجح: ${this.passedTests} ✅`);
        console.log(`فشل: ${this.failedTests} ❌`);
        console.log(`معدل النجاح: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%`);
        
        if (this.failedTests > 0) {
            console.log('\n❌ الاختبارات الفاشلة:');
            this.testResults
                .filter(test => test.status !== 'PASS')
                .forEach(test => {
                    console.log(`- ${test.name}: ${test.error || 'فشل'}`);
                });
        }
        
        // عرض إشعار للمستخدم
        if (typeof window.showEnhancedToast === 'function') {
            const message = `اكتملت الاختبارات: ${this.passedTests}/${this.totalTests} نجح`;
            const type = this.failedTests === 0 ? 'success' : 'warning';
            window.showEnhancedToast(message, type, 5000);
        }
        
        return {
            total: this.totalTests,
            passed: this.passedTests,
            failed: this.failedTests,
            results: this.testResults
        };
    }

    /**
     * اختبار سريع للنظام
     */
    async quickTest() {
        console.log('⚡ اختبار سريع للنظام...');
        
        const quickTests = [
            () => typeof window.systemIntegration !== 'undefined',
            () => typeof window.customersManager !== 'undefined',
            () => typeof window.dbManager !== 'undefined',
            () => typeof window.bugFixes !== 'undefined'
        ];
        
        const results = quickTests.map(test => test());
        const passed = results.filter(Boolean).length;
        
        console.log(`اختبار سريع: ${passed}/${quickTests.length} مكونات جاهزة`);
        
        return passed === quickTests.length;
    }
}

// إنشاء مثيل من نظام الاختبار
const systemTester = new SystemTester();

// تصدير النظام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = systemTester;
} else {
    window.systemTester = systemTester;
}

// تشغيل اختبار سريع عند التحميل
if (typeof window !== 'undefined') {
    window.addEventListener('load', () => {
        setTimeout(() => {
            systemTester.quickTest();
        }, 2000);
    });
}
