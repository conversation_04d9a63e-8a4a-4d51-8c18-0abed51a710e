// مدير النسخ الاحتياطية
const fs = require('fs-extra');
const path = require('path');
const { app } = require('electron');
const log = require('electron-log');

class BackupManager {
    constructor() {
        this.backupDir = path.join(app.getPath('userData'), 'backups');
        this.dataFile = path.join(app.getPath('userData'), 'data.json');
        this.maxBackups = 10;
        this.backupInterval = 30 * 60 * 1000; // 30 دقيقة
        this.autoBackupTimer = null;
        
        this.init();
    }

    async init() {
        try {
            // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
            await fs.ensureDir(this.backupDir);
            log.info('تم تهيئة مدير النسخ الاحتياطية');
            
            // بدء النسخ الاحتياطي التلقائي
            this.startAutoBackup();
        } catch (error) {
            log.error('خطأ في تهيئة مدير النسخ الاحتياطية:', error);
        }
    }

    // إنشاء نسخة احتياطية يدوية
    async createBackup(description = '') {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupFileName = `backup_${timestamp}.json`;
            const backupPath = path.join(this.backupDir, backupFileName);

            // التحقق من وجود ملف البيانات
            if (!await fs.pathExists(this.dataFile)) {
                log.warn('ملف البيانات غير موجود، لا يمكن إنشاء نسخة احتياطية');
                return false;
            }

            // نسخ ملف البيانات
            await fs.copy(this.dataFile, backupPath);

            // إضافة معلومات إضافية للنسخة الاحتياطية
            const backupInfo = {
                timestamp: new Date().toISOString(),
                description: description,
                version: app.getVersion(),
                size: (await fs.stat(backupPath)).size,
                originalFile: this.dataFile
            };

            // حفظ معلومات النسخة الاحتياطية
            const infoPath = path.join(this.backupDir, `backup_${timestamp}.info.json`);
            await fs.writeJson(infoPath, backupInfo, { spaces: 2 });

            log.info(`تم إنشاء نسخة احتياطية: ${backupFileName}`);

            // تنظيف النسخ القديمة
            await this.cleanOldBackups();

            return {
                success: true,
                fileName: backupFileName,
                path: backupPath,
                info: backupInfo
            };
        } catch (error) {
            log.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            return { success: false, error: error.message };
        }
    }

    // استعادة نسخة احتياطية
    async restoreBackup(backupFileName) {
        try {
            const backupPath = path.join(this.backupDir, backupFileName);

            // التحقق من وجود النسخة الاحتياطية
            if (!await fs.pathExists(backupPath)) {
                throw new Error('النسخة الاحتياطية غير موجودة');
            }

            // إنشاء نسخة احتياطية من الملف الحالي قبل الاستعادة
            await this.createBackup('قبل الاستعادة');

            // استعادة النسخة الاحتياطية
            await fs.copy(backupPath, this.dataFile);

            log.info(`تم استعادة النسخة الاحتياطية: ${backupFileName}`);
            return { success: true };
        } catch (error) {
            log.error('خطأ في استعادة النسخة الاحتياطية:', error);
            return { success: false, error: error.message };
        }
    }

    // الحصول على قائمة النسخ الاحتياطية
    async getBackupList() {
        try {
            const files = await fs.readdir(this.backupDir);
            const backups = [];

            for (const file of files) {
                if (file.endsWith('.json') && !file.endsWith('.info.json')) {
                    const backupPath = path.join(this.backupDir, file);
                    const infoPath = path.join(this.backupDir, file.replace('.json', '.info.json'));
                    
                    const stats = await fs.stat(backupPath);
                    let info = {};

                    // قراءة معلومات النسخة الاحتياطية إذا كانت متوفرة
                    if (await fs.pathExists(infoPath)) {
                        info = await fs.readJson(infoPath);
                    }

                    backups.push({
                        fileName: file,
                        path: backupPath,
                        size: stats.size,
                        created: stats.birthtime,
                        modified: stats.mtime,
                        ...info
                    });
                }
            }

            // ترتيب النسخ حسب التاريخ (الأحدث أولاً)
            backups.sort((a, b) => new Date(b.created) - new Date(a.created));

            return backups;
        } catch (error) {
            log.error('خطأ في الحصول على قائمة النسخ الاحتياطية:', error);
            return [];
        }
    }

    // حذف نسخة احتياطية
    async deleteBackup(backupFileName) {
        try {
            const backupPath = path.join(this.backupDir, backupFileName);
            const infoPath = path.join(this.backupDir, backupFileName.replace('.json', '.info.json'));

            // حذف ملف النسخة الاحتياطية
            if (await fs.pathExists(backupPath)) {
                await fs.remove(backupPath);
            }

            // حذف ملف المعلومات
            if (await fs.pathExists(infoPath)) {
                await fs.remove(infoPath);
            }

            log.info(`تم حذف النسخة الاحتياطية: ${backupFileName}`);
            return { success: true };
        } catch (error) {
            log.error('خطأ في حذف النسخة الاحتياطية:', error);
            return { success: false, error: error.message };
        }
    }

    // تنظيف النسخ القديمة
    async cleanOldBackups() {
        try {
            const backups = await this.getBackupList();
            
            if (backups.length > this.maxBackups) {
                const backupsToDelete = backups.slice(this.maxBackups);
                
                for (const backup of backupsToDelete) {
                    await this.deleteBackup(backup.fileName);
                }

                log.info(`تم حذف ${backupsToDelete.length} نسخة احتياطية قديمة`);
            }
        } catch (error) {
            log.error('خطأ في تنظيف النسخ القديمة:', error);
        }
    }

    // بدء النسخ الاحتياطي التلقائي
    startAutoBackup() {
        if (this.autoBackupTimer) {
            clearInterval(this.autoBackupTimer);
        }

        this.autoBackupTimer = setInterval(async () => {
            await this.createBackup('نسخة احتياطية تلقائية');
        }, this.backupInterval);

        log.info('تم بدء النسخ الاحتياطي التلقائي');
    }

    // إيقاف النسخ الاحتياطي التلقائي
    stopAutoBackup() {
        if (this.autoBackupTimer) {
            clearInterval(this.autoBackupTimer);
            this.autoBackupTimer = null;
            log.info('تم إيقاف النسخ الاحتياطي التلقائي');
        }
    }

    // تصدير النسخ الاحتياطية
    async exportBackups(exportPath) {
        try {
            await fs.copy(this.backupDir, exportPath);
            log.info(`تم تصدير النسخ الاحتياطية إلى: ${exportPath}`);
            return { success: true };
        } catch (error) {
            log.error('خطأ في تصدير النسخ الاحتياطية:', error);
            return { success: false, error: error.message };
        }
    }

    // استيراد النسخ الاحتياطية
    async importBackups(importPath) {
        try {
            await fs.copy(importPath, this.backupDir, { overwrite: false });
            log.info(`تم استيراد النسخ الاحتياطية من: ${importPath}`);
            return { success: true };
        } catch (error) {
            log.error('خطأ في استيراد النسخ الاحتياطية:', error);
            return { success: false, error: error.message };
        }
    }
}

module.exports = BackupManager;
