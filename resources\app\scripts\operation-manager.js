// نظام إدارة العمليات التلقائي
// Automatic Operation Management System

class OperationManager {
    constructor() {
        this.isInitialized = false;
    }

    /**
     * تهيئة نظام إدارة العمليات
     */
    initialize() {
        if (this.isInitialized) return;

        console.log('🔧 تهيئة نظام إدارة العمليات التلقائي...');

        // ربط أحداث نموذج الزبون
        this.setupCustomerFormIntegration();

        // ربط أحداث جدول الإرسال
        this.setupTransmissionTableIntegration();

        // ربط أحداث إدارة بطاقات الغاز
        this.setupGasCardsIntegration();

        this.isInitialized = true;
        console.log('✅ تم تهيئة نظام إدارة العمليات التلقائي');
    }

    /**
     * ربط نموذج الزبون مع نظام العمليات
     */
    setupCustomerFormIntegration() {
        // مراقبة إرسال نموذج الزبون
        const customerForm = document.getElementById('customer-form');
        if (customerForm) {
            // إضافة مستمع للنموذج
            customerForm.addEventListener('submit', (e) => {
                // السماح للنموذج بالإرسال أولاً
                setTimeout(() => {
                    this.handleCustomerFormSubmission();
                }, 500);
            });
        }

        // مراقبة تغيير نوع العملية
        const operationTypeSelect = document.getElementById('operation-type');
        if (operationTypeSelect) {
            operationTypeSelect.addEventListener('change', (e) => {
                this.updateOperationTypeUI(e.target.value);
            });
        }
    }

    /**
     * معالجة إرسال نموذج الزبون
     */
    async handleCustomerFormSubmission() {
        try {
            const operationType = document.getElementById('operation-type')?.value;
            const operationDate = document.getElementById('operation-date')?.value;
            const operationNotes = document.getElementById('operation-notes')?.value;

            if (!operationType) {
                console.log('ℹ️ لم يتم تحديد نوع عملية');
                return;
            }

            // جمع بيانات الزبون والسيارة
            const customerData = this.getCustomerDataFromForm();
            const vehicleData = this.getVehicleDataFromForm();
            const tankData = this.getTankDataFromForm();

            if (!customerData || !vehicleData) {
                console.error('❌ بيانات الزبون أو السيارة غير مكتملة');
                return;
            }

            // معالجة العملية حسب النوع
            switch (operationType) {
                case 'تركيب':
                case 'مراقبة':
                    await this.addToTransmissionTable({
                        operationType,
                        operationDate,
                        operationNotes,
                        customerData,
                        vehicleData,
                        tankData
                    });
                    break;

                case 'تجديد':
                    await this.addToGasCards({
                        operationType,
                        operationDate,
                        operationNotes,
                        customerData,
                        vehicleData,
                        tankData
                    });
                    break;

                default:
                    console.warn('⚠️ نوع عملية غير معروف:', operationType);
            }

        } catch (error) {
            console.error('❌ خطأ في معالجة العملية:', error);
        }
    }

    /**
     * جمع بيانات الزبون من النموذج
     */
    getCustomerDataFromForm() {
        try {
            return {
                id: document.getElementById('customer-id')?.value || null,
                name: document.getElementById('customer-name')?.value,
                phone: document.getElementById('customer-phone')?.value,
                email: document.getElementById('customer-email')?.value,
                address: document.getElementById('customer-address')?.value
            };
        } catch (error) {
            console.error('❌ خطأ في جمع بيانات الزبون:', error);
            return null;
        }
    }

    /**
     * جمع بيانات السيارة من النموذج
     */
    getVehicleDataFromForm() {
        try {
            return {
                plateNumber: document.getElementById('plate-number')?.value,
                brand: document.getElementById('vehicle-brand')?.value,
                model: document.getElementById('vehicle-model')?.value,
                year: document.getElementById('vehicle-year')?.value,
                color: document.getElementById('vehicle-color')?.value
            };
        } catch (error) {
            console.error('❌ خطأ في جمع بيانات السيارة:', error);
            return null;
        }
    }

    /**
     * جمع بيانات الخزان من النموذج
     */
    getTankDataFromForm() {
        try {
            return {
                type: document.getElementById('tank-type')?.value,
                brand: document.getElementById('tank-brand')?.value,
                serialNumber: document.getElementById('tank-serial-number')?.value,
                capacity: document.getElementById('tank-capacity')?.value
            };
        } catch (error) {
            console.error('❌ خطأ في جمع بيانات الخزان:', error);
            return null;
        }
    }

    /**
     * إضافة عملية إلى جدول الإرسال
     */
    async addToTransmissionTable(operationData) {
        try {
            console.log('📋 إضافة عملية إلى جدول الإرسال:', operationData.operationType);

            const transmissionEntry = {
                type: operationData.operationType,
                tankNumber: operationData.tankData?.serialNumber || '',
                carType: `${operationData.vehicleData?.brand || ''} ${operationData.vehicleData?.model || ''}`.trim(),
                serialNumber: operationData.tankData?.serialNumber || '',
                registrationNumber: operationData.vehicleData?.plateNumber || '',
                ownerName: operationData.customerData?.name || '',
                phoneNumber: operationData.customerData?.phone || '',
                operationDate: operationData.operationDate || new Date().toISOString().split('T')[0],
                notes: operationData.operationNotes || '',
                source: 'customer_form',
                sourceId: operationData.customerData?.id,
                status: 'مكتمل'
            };

            // إضافة إلى جدول الإرسال
            if (window.transmissionManager) {
                const addedEntry = window.transmissionManager.addEntry(transmissionEntry);
                console.log('✅ تم إضافة العملية إلى جدول الإرسال:', addedEntry);
                
                // عرض إشعار
                this.showSuccessMessage(`تم إضافة عملية ${operationData.operationType} إلى جدول الإرسال`);
            } else {
                // إضافة إلى البيانات المحلية
                this.addToLocalTransmissionData(transmissionEntry);
            }

        } catch (error) {
            console.error('❌ خطأ في إضافة العملية إلى جدول الإرسال:', error);
            this.showErrorMessage('فشل في إضافة العملية إلى جدول الإرسال');
        }
    }

    /**
     * إضافة بطاقة غاز جديدة
     */
    async addToGasCards(operationData) {
        try {
            console.log('💳 إضافة بطاقة غاز جديدة');

            const gasCard = {
                id: 'card-' + Date.now(),
                customerId: operationData.customerData?.id,
                vehicleId: null, // سيتم تحديثه لاحقاً
                cardNumber: this.generateCardNumber(),
                vehicleNumber: operationData.vehicleData?.plateNumber || '',
                customerName: operationData.customerData?.name || '',
                tankNumber: operationData.tankData?.serialNumber || '',
                serialNumber: operationData.tankData?.serialNumber || '',
                issueDate: operationData.operationDate || new Date().toISOString().split('T')[0],
                expiryDate: this.calculateExpiryDate(operationData.operationDate),
                notes: operationData.operationNotes || '',
                operationType: 'تجديد بطاقة',
                operationDate: operationData.operationDate,
                status: 'active',
                createdAt: new Date().toISOString()
            };

            // إضافة إلى بطاقات الغاز
            this.addToLocalGasCardsData(gasCard);

            console.log('✅ تم إضافة بطاقة الغاز الجديدة:', gasCard);
            this.showSuccessMessage('تم إضافة بطاقة غاز جديدة بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إضافة بطاقة الغاز:', error);
            this.showErrorMessage('فشل في إضافة بطاقة الغاز');
        }
    }

    /**
     * إضافة إلى البيانات المحلية لجدول الإرسال
     */
    addToLocalTransmissionData(entry) {
        try {
            // التأكد من وجود البيانات
            if (!window.appData) {
                window.appData = {};
            }
            if (!window.appData.transmissionTable) {
                window.appData.transmissionTable = [];
            }

            // إضافة العملية
            window.appData.transmissionTable.push(entry);

            // حفظ البيانات
            if (typeof saveData === 'function') {
                saveData();
            }

            console.log('✅ تم إضافة العملية إلى البيانات المحلية');

        } catch (error) {
            console.error('❌ خطأ في إضافة العملية إلى البيانات المحلية:', error);
        }
    }

    /**
     * إضافة إلى البيانات المحلية لبطاقات الغاز
     */
    addToLocalGasCardsData(gasCard) {
        try {
            // التأكد من وجود البيانات
            if (!window.appData) {
                window.appData = {};
            }
            if (!window.appData.gasCards) {
                window.appData.gasCards = [];
            }

            // إضافة البطاقة
            window.appData.gasCards.push(gasCard);

            // حفظ البيانات
            if (typeof saveData === 'function') {
                saveData();
            }

            // تحديث جدول بطاقات الغاز إذا كان مفتوحاً
            if (typeof updateGasCardsTable === 'function') {
                updateGasCardsTable();
            }

            console.log('✅ تم إضافة بطاقة الغاز إلى البيانات المحلية');

        } catch (error) {
            console.error('❌ خطأ في إضافة بطاقة الغاز إلى البيانات المحلية:', error);
        }
    }

    /**
     * توليد رقم بطاقة غاز
     */
    generateCardNumber() {
        const prefix = 'GC';
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `${prefix}-${timestamp}-${random}`;
    }

    /**
     * حساب تاريخ انتهاء البطاقة (سنة من تاريخ الإصدار)
     */
    calculateExpiryDate(issueDate) {
        const issue = new Date(issueDate || new Date());
        const expiry = new Date(issue);
        expiry.setFullYear(expiry.getFullYear() + 1);
        return expiry.toISOString().split('T')[0];
    }

    /**
     * تحديث واجهة المستخدم حسب نوع العملية
     */
    updateOperationTypeUI(operationType) {
        const operationDateLabel = document.getElementById('operation-date-label');
        const operationNotesLabel = document.querySelector('label[for="operation-notes"]');

        if (operationDateLabel) {
            switch (operationType) {
                case 'تركيب':
                    operationDateLabel.textContent = 'تاريخ التركيب:';
                    break;
                case 'مراقبة':
                    operationDateLabel.textContent = 'تاريخ المراقبة:';
                    break;
                case 'تجديد':
                    operationDateLabel.textContent = 'تاريخ التجديد:';
                    break;
                default:
                    operationDateLabel.textContent = 'تاريخ العملية:';
            }
        }

        if (operationNotesLabel) {
            switch (operationType) {
                case 'تركيب':
                    operationNotesLabel.textContent = 'ملاحظات التركيب:';
                    break;
                case 'مراقبة':
                    operationNotesLabel.textContent = 'ملاحظات المراقبة:';
                    break;
                case 'تجديد':
                    operationNotesLabel.textContent = 'ملاحظات التجديد:';
                    break;
                default:
                    operationNotesLabel.textContent = 'ملاحظات:';
            }
        }
    }

    /**
     * ربط جدول الإرسال
     */
    setupTransmissionTableIntegration() {
        // سيتم تنفيذ هذا لاحقاً إذا لزم الأمر
        console.log('📋 تم ربط جدول الإرسال');
    }

    /**
     * ربط إدارة بطاقات الغاز
     */
    setupGasCardsIntegration() {
        // سيتم تنفيذ هذا لاحقاً إذا لزم الأمر
        console.log('💳 تم ربط إدارة بطاقات الغاز');
    }

    /**
     * عرض رسالة نجاح
     */
    showSuccessMessage(message) {
        if (window.showEnhancedToast) {
            window.showEnhancedToast(message, 'success');
        } else if (window.showToast) {
            window.showToast(message, true);
        } else {
            console.log('✅ ' + message);
        }
    }

    /**
     * عرض رسالة خطأ
     */
    showErrorMessage(message) {
        if (window.showEnhancedToast) {
            window.showEnhancedToast(message, 'error');
        } else if (window.showToast) {
            window.showToast(message, false);
        } else {
            console.error('❌ ' + message);
        }
    }
}

// إنشاء مثيل من مدير العمليات
const operationManager = new OperationManager();

// تصدير المدير
if (typeof module !== 'undefined' && module.exports) {
    module.exports = operationManager;
} else {
    window.operationManager = operationManager;
}

// تهيئة تلقائية عند تحميل الصفحة
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            operationManager.initialize();
        }, 1500);
    });
}
