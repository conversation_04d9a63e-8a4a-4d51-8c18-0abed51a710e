@echo off
chcp 65001 >nul
title تشغيل تطبيق مؤسسة وقود المستقبل

echo ========================================
echo    تطبيق مؤسسة وقود المستقبل
echo    Future Fuel Corporation
echo    الإصدار 2.2.0
echo ========================================
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo ⚠️  لم يتم العثور على مجلد node_modules
    echo يجب تثبيت التبعيات أولاً
    echo.
    echo هل تريد تثبيت التبعيات الآن؟ (y/n)
    set /p install_deps=
    if /i "%install_deps%"=="y" (
        echo.
        echo جاري تثبيت التبعيات...
        call install-electron.bat
        echo.
    ) else (
        echo يرجى تشغيل install-electron.bat أولاً
        pause
        exit /b 1
    )
)

echo 🚀 بدء تشغيل التطبيق...
echo.

REM Check if Electron is installed
npm list electron --depth=0 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Electron غير مثبت
    echo يرجى تشغيل install-electron.bat أولاً
    pause
    exit /b 1
)

echo ✅ جميع التبعيات متوفرة
echo 📱 تشغيل تطبيق Electron...
echo.

REM Start the application
npm start

echo.
echo تم إغلاق التطبيق
pause
