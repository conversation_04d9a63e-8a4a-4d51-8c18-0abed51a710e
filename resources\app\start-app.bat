@echo off
title Future Fuel Corporation - مؤسسة وقود المستقبل

echo ========================================
echo    Future Fuel Corporation
echo    مؤسسة وقود المستقبل
echo    Version 2.2.0
echo ========================================
echo.

echo Starting application...
echo تشغيل التطبيق...
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo Warning: node_modules folder not found
    echo تحذير: مجلد node_modules غير موجود
    echo.
    echo Installing dependencies...
    echo تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo Failed to install dependencies
        echo فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
)

echo Launching Electron app...
echo تشغيل تطبيق Electron...
echo.

REM Start the application
npm start

echo.
echo Application closed
echo تم إغلاق التطبيق
pause
