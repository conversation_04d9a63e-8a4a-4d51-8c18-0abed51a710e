@echo off
title Uninstalling Future Fuel Management System
color 0C

echo =============================================
echo    Future Fuel Management System Uninstaller
echo                Version 2.2.0
echo =============================================
echo.
echo WARNING: This will remove all application files!
echo Your data will be backed up before removal.
echo Press any key to continue or close this window to cancel...
pause >nul

echo.
echo [1/5] Creating backup of user data...
set INSTALL_DIR=%USERPROFILE%\Future Fuel Management
set BACKUP_DIR=%USERPROFILE%\Desktop\Future Fuel Backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%
if exist "%INSTALL_DIR%\data.json" (
    mkdir "%BACKUP_DIR%" 2>nul
    copy "%INSTALL_DIR%\data.json" "%BACKUP_DIR%\data_backup.json" >nul 2>&1
    copy "%INSTALL_DIR%\gasShopData*" "%BACKUP_DIR%\" >nul 2>&1
    echo OK: Data backed up to %BACKUP_DIR%
) else (
    echo INFO: No data files found to backup
)

echo.
echo [2/5] Removing desktop shortcut...
del "%USERPROFILE%\Desktop\Future Fuel Management.lnk" 2>nul
echo OK: Desktop shortcut removed

echo.
echo [3/5] Removing start menu entry...
rmdir /s /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Future Fuel" 2>nul
echo OK: Start menu entry removed

echo.
echo [4/5] Removing application files...
if exist "%INSTALL_DIR%" (
    rmdir /s /q "%INSTALL_DIR%"
    echo OK: Application files removed
) else (
    echo INFO: Application directory not found
)

echo.
echo [5/5] Cleaning up temporary files...
del "%TEMP%\gasShop*" 2>nul
echo OK: Temporary files cleaned

echo.
echo =============================================
echo        Uninstallation Completed Successfully!
echo =============================================
echo.
echo All application files have been removed.
if exist "%BACKUP_DIR%" (
    echo Your data has been backed up to: %BACKUP_DIR%
)
echo Thank you for using Future Fuel Management System!
echo.
echo Press any key to exit...
pause >nul
