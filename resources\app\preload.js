const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// تعريض APIs آمنة للواجهة الأمامية
contextBridge.exposeInMainWorld('electronAPI', {
    // حفظ البيانات
    saveData: (data) => {
        try {
            const fs = require('fs');
            const path = require('path');
            const dataPath = path.join(__dirname, 'data.json');
            fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    },

    // تحميل البيانات
    loadData: () => {
        try {
            const fs = require('fs');
            const path = require('path');
            const dataPath = path.join(__dirname, 'data.json');
            if (fs.existsSync(dataPath)) {
                const data = fs.readFileSync(dataPath, 'utf8');
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            return null;
        }
    },

    // إنشاء نسخة احتياطية
    createBackup: () => {
        try {
            const fs = require('fs');
            const path = require('path');
            const dataPath = path.join(__dirname, 'data.json');
            const backupPath = path.join(__dirname, `backup_${Date.now()}.json`);
            
            if (fs.existsSync(dataPath)) {
                fs.copyFileSync(dataPath, backupPath);
                return true;
            }
            return false;
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            return false;
        }
    },

    // معالجة مربعات الحوار
    showSaveDialog: (options) => ipcRenderer.invoke('save-dialog', options),
    showOpenDialog: (options) => ipcRenderer.invoke('open-dialog', options),

    // فتح روابط خارجية
    openExternal: (url) => ipcRenderer.invoke('open-external', url),

    // معلومات النظام
    getSystemInfo: () => ipcRenderer.invoke('get-system-info'),

    // إدارة الثيم
    setTheme: (theme) => ipcRenderer.invoke('set-theme', theme),
    getTheme: () => ipcRenderer.invoke('get-theme'),

    // إشعارات
    createAutoBackup: () => ipcRenderer.send('create-auto-backup'),

    // إشعارات النظام
    showNotification: (title, body, icon) => {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, { body, icon });
        }
    },

    // طلب إذن الإشعارات
    requestNotificationPermission: async () => {
        if ('Notification' in window) {
            return await Notification.requestPermission();
        }
        return 'denied';
    }
});
