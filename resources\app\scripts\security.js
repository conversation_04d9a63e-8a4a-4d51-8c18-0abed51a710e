// Future Fuel Management System - Advanced Security System
// Version 2.2.0 - Professional security and access control

class SecurityManager {
    constructor() {
        this.sessionTimeout = 30 * 60 * 1000; // 30 minutes
        this.maxLoginAttempts = 5;
        this.lockoutDuration = 15 * 60 * 1000; // 15 minutes
        this.encryptionKey = this.generateEncryptionKey();

        this.init();
    }

    init() {
        this.setupSessionManagement();
        this.setupDataEncryption();
        this.setupAccessControl();
        this.setupSecurityMonitoring();
    }

    setupSessionManagement() {
        // Check session on page load
        this.checkSession();

        // Set up session timeout
        this.resetSessionTimer();

        // Monitor user activity
        this.setupActivityMonitoring();
    }

    checkSession() {
        const session = this.getSession();
        if (session && this.isSessionValid(session)) {
            this.currentUser = session.user;
            this.updateLastActivity();
        } else {
            this.clearSession();
            // For now, we'll create a default session
            this.createDefaultSession();
        }
    }

    isSessionValid(session) {
        const now = new Date().getTime();
        const sessionAge = now - session.createdAt;
        const lastActivity = now - session.lastActivity;

        return sessionAge < (24 * 60 * 60 * 1000) && // 24 hours max
               lastActivity < this.sessionTimeout;
    }

    createDefaultSession() {
        // Create a default admin session for the standalone app
        const session = {
            user: {
                id: 'admin',
                name: 'مدير النظام',
                role: 'admin',
                permissions: ['all']
            },
            createdAt: new Date().getTime(),
            lastActivity: new Date().getTime(),
            sessionId: this.generateSessionId()
        };

        this.setSession(session);
        this.currentUser = session.user;
    }

    generateSessionId() {
        return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }

    getSession() {
        try {
            const sessionData = localStorage.getItem('app_session');
            return sessionData ? JSON.parse(this.decrypt(sessionData)) : null;
        } catch (error) {
            console.warn('Failed to retrieve session:', error);
            return null;
        }
    }

    setSession(session) {
        try {
            const encryptedSession = this.encrypt(JSON.stringify(session));
            localStorage.setItem('app_session', encryptedSession);
        } catch (error) {
            console.error('Failed to save session:', error);
        }
    }

    clearSession() {
        localStorage.removeItem('app_session');
        this.currentUser = null;
    }

    updateLastActivity() {
        const session = this.getSession();
        if (session) {
            session.lastActivity = new Date().getTime();
            this.setSession(session);
        }
    }

    resetSessionTimer() {
        if (this.sessionTimer) {
            clearTimeout(this.sessionTimer);
        }

        this.sessionTimer = setTimeout(() => {
            this.handleSessionTimeout();
        }, this.sessionTimeout);
    }

    handleSessionTimeout() {
        if (typeof notificationManager !== 'undefined') {
            notificationManager.warning(
                'انتهت الجلسة',
                'تم إنهاء الجلسة بسبب عدم النشاط. سيتم إعادة تشغيل التطبيق.',
                { persistent: true }
            );
        }

        setTimeout(() => {
            window.location.reload();
        }, 3000);
    }

    setupActivityMonitoring() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

        events.forEach(event => {
            document.addEventListener(event, () => {
                this.updateLastActivity();
                this.resetSessionTimer();
            }, true);
        });
    }

    setupDataEncryption() {
        // Override localStorage methods to add encryption
        this.originalSetItem = localStorage.setItem.bind(localStorage);
        this.originalGetItem = localStorage.getItem.bind(localStorage);

        // Encrypt sensitive data keys
        this.sensitiveKeys = [
            'gasShopData',
            'app_settings',
            'backup_',
            'telegram_settings'
        ];
    }

    setupAccessControl() {
        // Define user roles and permissions
        this.roles = {
            admin: {
                name: 'مدير النظام',
                permissions: ['all']
            },
            manager: {
                name: 'مدير',
                permissions: ['read', 'write', 'reports', 'customers', 'sales', 'inventory']
            },
            employee: {
                name: 'موظف',
                permissions: ['read', 'customers', 'appointments']
            },
            viewer: {
                name: 'مشاهد',
                permissions: ['read']
            }
        };
    }

    setupSecurityMonitoring() {
        // Monitor for suspicious activities
        this.securityLog = [];
        this.setupSecurityEventListeners();
    }

    setupSecurityEventListeners() {
        // Monitor for potential security threats
        window.addEventListener('beforeunload', () => {
            this.logSecurityEvent('session_end', 'User session ended');
        });

        // Monitor for developer tools
        let devtools = {open: false, orientation: null};
        setInterval(() => {
            if (window.outerHeight - window.innerHeight > 200 ||
                window.outerWidth - window.innerWidth > 200) {
                if (!devtools.open) {
                    devtools.open = true;
                    this.logSecurityEvent('devtools_opened', 'Developer tools opened');
                }
            } else {
                devtools.open = false;
            }
        }, 500);
    }

    // Encryption methods
    generateEncryptionKey() {
        // Generate a simple key based on browser fingerprint
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Security key generation', 2, 2);

        const fingerprint = canvas.toDataURL();
        return btoa(fingerprint.slice(-50));
    }

    encrypt(text) {
        try {
            // Simple XOR encryption (for basic protection)
            let result = '';
            for (let i = 0; i < text.length; i++) {
                result += String.fromCharCode(
                    text.charCodeAt(i) ^ this.encryptionKey.charCodeAt(i % this.encryptionKey.length)
                );
            }
            return btoa(result);
        } catch (error) {
            console.warn('Encryption failed, storing as plain text');
            return text;
        }
    }

    decrypt(encryptedText) {
        try {
            const text = atob(encryptedText);
            let result = '';
            for (let i = 0; i < text.length; i++) {
                result += String.fromCharCode(
                    text.charCodeAt(i) ^ this.encryptionKey.charCodeAt(i % this.encryptionKey.length)
                );
            }
            return result;
        } catch (error) {
            console.warn('Decryption failed, returning as is');
            return encryptedText;
        }
    }

    // Access control methods
    hasPermission(permission) {
        if (!this.currentUser) return false;

        const userRole = this.roles[this.currentUser.role];
        if (!userRole) return false;

        return userRole.permissions.includes('all') ||
               userRole.permissions.includes(permission);
    }

    requirePermission(permission, action = 'perform this action') {
        if (!this.hasPermission(permission)) {
            if (typeof notificationManager !== 'undefined') {
                notificationManager.error(
                    'غير مصرح',
                    `ليس لديك صلاحية لـ ${action}`
                );
            }
            return false;
        }
        return true;
    }

    // Security logging
    logSecurityEvent(type, description, severity = 'info') {
        const event = {
            timestamp: new Date().toISOString(),
            type: type,
            description: description,
            severity: severity,
            user: this.currentUser?.id || 'anonymous',
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        this.securityLog.push(event);

        // Keep only last 100 events
        if (this.securityLog.length > 100) {
            this.securityLog = this.securityLog.slice(-100);
        }

        // Store security log
        try {
            localStorage.setItem('security_log', JSON.stringify(this.securityLog));
        } catch (error) {
            console.warn('Failed to save security log');
        }

        // Log to console for debugging
        console.log(`Security Event [${severity.toUpperCase()}]:`, event);
    }

    // Data validation and sanitization
    sanitizeInput(input) {
        if (typeof input !== 'string') return input;

        // Remove potentially dangerous characters
        return input
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '')
            .trim();
    }

    validateData(data, schema) {
        // Basic data validation
        if (!data || !schema) return false;

        for (const field in schema) {
            const value = data[field];
            const rules = schema[field];

            if (rules.required && (value === undefined || value === null || value === '')) {
                return false;
            }

            if (value && rules.type && typeof value !== rules.type) {
                return false;
            }

            if (value && rules.maxLength && value.length > rules.maxLength) {
                return false;
            }

            if (value && rules.pattern && !rules.pattern.test(value)) {
                return false;
            }
        }

        return true;
    }

    // Secure data storage
    secureSetItem(key, value) {
        try {
            const shouldEncrypt = this.sensitiveKeys.some(sensitiveKey =>
                key.startsWith(sensitiveKey)
            );

            if (shouldEncrypt) {
                value = this.encrypt(value);
            }

            this.originalSetItem(key, value);
            this.logSecurityEvent('data_write', `Data written to ${key}`);
        } catch (error) {
            this.logSecurityEvent('data_write_error', `Failed to write data to ${key}`, 'error');
            throw error;
        }
    }

    secureGetItem(key) {
        try {
            let value = this.originalGetItem(key);

            if (value) {
                const shouldDecrypt = this.sensitiveKeys.some(sensitiveKey =>
                    key.startsWith(sensitiveKey)
                );

                if (shouldDecrypt) {
                    value = this.decrypt(value);
                }

                this.logSecurityEvent('data_read', `Data read from ${key}`);
            }

            return value;
        } catch (error) {
            this.logSecurityEvent('data_read_error', `Failed to read data from ${key}`, 'error');
            return null;
        }
    }

    // Security report
    generateSecurityReport() {
        const report = {
            timestamp: new Date().toISOString(),
            currentUser: this.currentUser,
            sessionInfo: {
                sessionAge: this.getSession() ?
                    new Date().getTime() - this.getSession().createdAt : 0,
                lastActivity: this.getSession() ?
                    new Date().getTime() - this.getSession().lastActivity : 0
            },
            securityEvents: this.securityLog.slice(-20), // Last 20 events
            systemInfo: {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine
            }
        };

        return report;
    }

    // Backup security
    createSecureBackup() {
        try {
            const data = this.getAllSecureData();
            const backup = {
                timestamp: new Date().toISOString(),
                version: '2.2.0',
                checksum: this.calculateChecksum(JSON.stringify(data)),
                data: data
            };

            const encryptedBackup = this.encrypt(JSON.stringify(backup));
            return encryptedBackup;
        } catch (error) {
            this.logSecurityEvent('backup_error', 'Failed to create secure backup', 'error');
            throw error;
        }
    }

    restoreSecureBackup(encryptedBackup) {
        try {
            const backupData = JSON.parse(this.decrypt(encryptedBackup));

            // Verify checksum
            const calculatedChecksum = this.calculateChecksum(JSON.stringify(backupData.data));
            if (calculatedChecksum !== backupData.checksum) {
                throw new Error('Backup integrity check failed');
            }

            // Restore data
            this.restoreSecureData(backupData.data);

            this.logSecurityEvent('backup_restore', 'Secure backup restored successfully');
            return true;
        } catch (error) {
            this.logSecurityEvent('backup_restore_error', 'Failed to restore secure backup', 'error');
            throw error;
        }
    }

    getAllSecureData() {
        const data = {};
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (this.sensitiveKeys.some(sensitiveKey => key.startsWith(sensitiveKey))) {
                data[key] = this.secureGetItem(key);
            }
        }
        return data;
    }

    restoreSecureData(data) {
        for (const key in data) {
            this.secureSetItem(key, data[key]);
        }
    }

    calculateChecksum(data) {
        let hash = 0;
        for (let i = 0; i < data.length; i++) {
            const char = data.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString(16);
    }
}

// Initialize security manager
const securityManager = new SecurityManager();

// Override global localStorage methods for security (commented out to avoid conflicts)
// Note: This override is disabled to prevent conflicts with existing code
// The security manager can be used directly when needed
/*
if (typeof window !== 'undefined') {
    const originalSetItem = localStorage.setItem;
    const originalGetItem = localStorage.getItem;

    localStorage.setItem = function(key, value) {
        return securityManager.secureSetItem(key, value);
    };

    localStorage.getItem = function(key) {
        return securityManager.secureGetItem(key);
    };
}
*/

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SecurityManager;
}
