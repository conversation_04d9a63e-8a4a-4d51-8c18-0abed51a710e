========================================
    مؤسسة وقود المستقبل
    دليل الاستخدام السريع
========================================

🎉 تم تثبيت التطبيق بنجاح على سطح المكتب!

📍 مكان الاختصارات:
• سطح المكتب: "Future Fuel Corporation"
• قائمة ابدأ: "Future Fuel Corporation"

🚀 طرق تشغيل التطبيق:

1️⃣ من سطح المكتب:
   - انقر نقرة مزدوجة على أيقونة "Future Fuel Corporation"

2️⃣ من قائمة ابدأ:
   - اضغط زر ابدأ (Start)
   - ابحث عن "Future Fuel Corporation"
   - انقر على التطبيق

3️⃣ من لوحة المفاتيح:
   - اضغط Windows + R
   - اكتب: C:\Users\<USER>\Desktop\HR\run-app.bat
   - اضغط Enter

🔧 إذا واجهت مشاكل:

❌ التطبيق لا يفتح:
   - تأكد من تثبيت Node.js
   - شغل install-electron.bat أولاً
   - جرب تشغيل run-app.bat مباشرة

❌ رسالة خطأ "npm غير موجود":
   - ثبت Node.js من: https://nodejs.org
   - أعد تشغيل الكمبيوتر
   - جرب مرة أخرى

❌ الأيقونة لا تظهر:
   - تأكد من وجود ملف icons\app-icon.ico
   - أعد إنشاء الاختصار بتشغيل create-shortcut-now.ps1

🛠️ ملفات مفيدة:

• run-app.bat - تشغيل التطبيق
• install-electron.bat - تثبيت التبعيات
• build-app.bat - بناء التطبيق
• create-shortcut-now.ps1 - إعادة إنشاء الاختصار

📱 ميزات التطبيق:

✅ إدارة بطاقات الغاز
✅ جدولة المواعيد
✅ إدارة الزبائن والموردين
✅ إدارة المخزون
✅ تتبع المبيعات والمشتريات
✅ إدارة الديون
✅ إصدار الشهادات
✅ تقارير شاملة
✅ نسخ احتياطية تلقائية
✅ واجهة عربية كاملة

🔄 التحديثات:

التطبيق يفحص التحديثات تلقائياً عند التشغيل
سيتم إشعارك عند توفر إصدار جديد

📞 الدعم الفني:

إذا احتجت مساعدة:
• راجع ملف README-ELECTRON.md
• تحقق من ملفات السجلات في مجلد logs
• تواصل مع فريق التطوير

========================================
مؤسسة وقود المستقبل - الإصدار 2.2.0
Future Fuel Corporation
========================================
