// تكامل النظام الجديد مع النظام القديم
// System Integration for Future Fuel Management

class SystemIntegration {
    constructor() {
        this.isInitialized = false;
        this.integrationStatus = {
            database: false,
            customersManager: false,
            bugFixes: false,
            migration: false
        };
    }

    /**
     * تهيئة النظام المتكامل
     */
    async initialize() {
        try {
            console.log('🚀 بدء تهيئة النظام المتكامل...');
            
            // تطبيق إصلاحات الأخطاء أولاً
            await this.applyBugFixes();
            
            // تهيئة قاعدة البيانات السحابية
            await this.initializeDatabase();
            
            // تهيئة مدير الزبائن
            await this.initializeCustomersManager();
            
            // تكامل مع النظام القديم
            await this.integrateWithLegacySystem();
            
            // إعداد المزامنة
            await this.setupSynchronization();
            
            // إعداد واجهة المستخدم المحسنة
            await this.setupEnhancedUI();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة النظام المتكامل بنجاح');
            
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة النظام المتكامل:', error);
            return false;
        }
    }

    /**
     * تطبيق إصلاحات الأخطاء
     */
    async applyBugFixes() {
        try {
            console.log('🔧 تطبيق إصلاحات الأخطاء...');
            
            if (window.bugFixes) {
                await window.bugFixes.applyAllFixes();
                this.integrationStatus.bugFixes = true;
                console.log('✅ تم تطبيق إصلاحات الأخطاء');
            }
        } catch (error) {
            console.error('❌ خطأ في تطبيق إصلاحات الأخطاء:', error);
        }
    }

    /**
     * تهيئة قاعدة البيانات السحابية
     */
    async initializeDatabase() {
        try {
            console.log('🗄️ تهيئة قاعدة البيانات السحابية...');
            
            if (window.dbManager) {
                const initialized = await window.dbManager.initialize();
                this.integrationStatus.database = initialized;
                
                if (initialized) {
                    console.log('✅ تم تهيئة قاعدة البيانات السحابية');
                } else {
                    console.log('⚠️ فشل في تهيئة قاعدة البيانات السحابية - سيتم استخدام الوضع المحلي');
                }
            }
        } catch (error) {
            console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
        }
    }

    /**
     * تهيئة مدير الزبائن
     */
    async initializeCustomersManager() {
        try {
            console.log('👥 تهيئة مدير الزبائن...');
            
            if (window.customersManager) {
                const initialized = await window.customersManager.initialize();
                this.integrationStatus.customersManager = initialized;
                
                if (initialized) {
                    console.log('✅ تم تهيئة مدير الزبائن');
                } else {
                    console.log('⚠️ فشل في تهيئة مدير الزبائن');
                }
            }
        } catch (error) {
            console.error('❌ خطأ في تهيئة مدير الزبائن:', error);
        }
    }

    /**
     * تكامل مع النظام القديم
     */
    async integrateWithLegacySystem() {
        try {
            console.log('🔗 تكامل مع النظام القديم...');
            
            // استبدال الوظائف القديمة بالجديدة
            this.replaceLegacyFunctions();
            
            // إعداد التوافق مع الكود القديم
            this.setupBackwardCompatibility();
            
            // ترحيل البيانات إذا لزم الأمر
            await this.migrateDataIfNeeded();
            
            console.log('✅ تم التكامل مع النظام القديم');
        } catch (error) {
            console.error('❌ خطأ في التكامل مع النظام القديم:', error);
        }
    }

    /**
     * استبدال الوظائف القديمة بالجديدة
     */
    replaceLegacyFunctions() {
        // استبدال وظائف إدارة الزبائن القديمة
        if (window.customersManager) {
            // حفظ الوظائف القديمة كنسخ احتياطية
            window.legacyFunctions = {
                addCustomer: window.addCustomer,
                updateCustomer: window.updateCustomer,
                deleteCustomer: window.deleteCustomer,
                searchCustomers: window.searchCustomers
            };

            // استبدال بالوظائف الجديدة مع الحفاظ على التوافق
            window.addCustomer = async function(customerData) {
                try {
                    // التأكد من تحميل البيانات أولاً
                    if (window.customersManager && window.customersManager.customers.length === 0) {
                        await window.customersManager.loadCustomers();
                    }

                    return await window.customersManager.addCustomer(customerData);
                } catch (error) {
                    console.error('خطأ في إضافة الزبون:', error);
                    // العودة للوظيفة القديمة في حالة الخطأ
                    if (window.legacyFunctions.addCustomer) {
                        return window.legacyFunctions.addCustomer(customerData);
                    }
                    throw error;
                }
            };

            window.updateCustomer = async function(customerId, customerData) {
                try {
                    return await window.customersManager.updateCustomer(customerId, customerData);
                } catch (error) {
                    console.error('خطأ في تحديث الزبون:', error);
                    if (window.legacyFunctions.updateCustomer) {
                        return window.legacyFunctions.updateCustomer(customerId, customerData);
                    }
                    throw error;
                }
            };

            window.deleteCustomer = async function(customerId) {
                try {
                    return await window.customersManager.deleteCustomer(customerId);
                } catch (error) {
                    console.error('خطأ في حذف الزبون:', error);
                    if (window.legacyFunctions.deleteCustomer) {
                        return window.legacyFunctions.deleteCustomer(customerId);
                    }
                    throw error;
                }
            };

            window.searchCustomers = async function(searchTerm, filters) {
                try {
                    return await window.customersManager.searchCustomers(searchTerm, filters);
                } catch (error) {
                    console.error('خطأ في البحث عن الزبائن:', error);
                    if (window.legacyFunctions.searchCustomers) {
                        return window.legacyFunctions.searchCustomers(searchTerm, filters);
                    }
                    return [];
                }
            };
        }

        // استبدال وظائف حفظ البيانات
        if (window.saveDataDebounced) {
            window.legacySaveData = window.saveData;
            window.saveData = function(data) {
                if (data) {
                    window.saveDataDebounced(data);
                } else {
                    window.saveDataDebounced(window.appData);
                }
            };
        }
    }

    /**
     * إعداد التوافق مع الكود القديم
     */
    setupBackwardCompatibility() {
        // التأكد من وجود متغير appData
        if (typeof window.appData === 'undefined') {
            window.appData = {
                customers: [],
                vehicles: [],
                gasCards: [],
                appointments: [],
                debts: [],
                settings: {}
            };
        }

        // إعداد مراقب للتغييرات في appData
        if (window.customersManager) {
            const originalAppData = window.appData;
            
            // مراقبة تغييرات الزبائن
            Object.defineProperty(window.appData, 'customers', {
                get: function() {
                    return window.customersManager.customers || [];
                },
                set: function(value) {
                    if (Array.isArray(value)) {
                        window.customersManager.customers = value;
                    }
                }
            });
        }

        // إعداد وظائف التحديث القديمة
        const originalUpdateCustomersTable = window.updateCustomersTable;
        window.updateCustomersTable = function() {
            if (window.customersManager) {
                const customers = window.customersManager.customers;
                
                // استخدام الوظيفة المحسنة إذا كانت متوفرة
                if (window.updateTableGeneric) {
                    const columns = [
                        'name',
                        'phone',
                        'email',
                        'address',
                        (customer) => `
                            <button onclick="editCustomer('${customer.id}')" class="btn btn-sm btn-primary">تعديل</button>
                            <button onclick="deleteCustomer('${customer.id}')" class="btn btn-sm btn-danger">حذف</button>
                        `
                    ];
                    window.updateTableGeneric('customers-table', customers, columns);
                } else if (originalUpdateCustomersTable) {
                    originalUpdateCustomersTable();
                }
            }
        };
    }

    /**
     * ترحيل البيانات إذا لزم الأمر
     */
    async migrateDataIfNeeded() {
        try {
            // التحقق من وجود بيانات قديمة تحتاج للترحيل
            const hasOldData = localStorage.getItem('gasShopData');
            const hasMigratedBefore = localStorage.getItem('dataMigrationCompleted');
            
            if (hasOldData && !hasMigratedBefore && window.dataMigration) {
                console.log('🔄 بدء ترحيل البيانات...');
                
                const migrationResult = await window.dataMigration.startMigration();
                if (migrationResult) {
                    localStorage.setItem('dataMigrationCompleted', 'true');
                    this.integrationStatus.migration = true;
                    console.log('✅ تم ترحيل البيانات بنجاح');
                }
            }
        } catch (error) {
            console.error('❌ خطأ في ترحيل البيانات:', error);
        }
    }

    /**
     * إعداد المزامنة
     */
    async setupSynchronization() {
        try {
            console.log('🔄 إعداد المزامنة...');
            
            // مزامنة دورية كل 5 دقائق
            setInterval(async () => {
                if (this.integrationStatus.database && this.integrationStatus.customersManager) {
                    try {
                        await window.customersManager.syncWithCloud();
                    } catch (error) {
                        console.error('خطأ في المزامنة:', error);
                    }
                }
            }, 5 * 60 * 1000);

            // مزامنة عند إغلاق النافذة
            window.addEventListener('beforeunload', async () => {
                if (this.integrationStatus.customersManager) {
                    try {
                        await window.customersManager.syncWithCloud();
                    } catch (error) {
                        console.error('خطأ في المزامنة النهائية:', error);
                    }
                }
            });

            console.log('✅ تم إعداد المزامنة');
        } catch (error) {
            console.error('❌ خطأ في إعداد المزامنة:', error);
        }
    }

    /**
     * إعداد واجهة المستخدم المحسنة
     */
    async setupEnhancedUI() {
        try {
            console.log('🎨 إعداد واجهة المستخدم المحسنة...');
            
            // إضافة مؤشرات حالة الاتصال
            this.addConnectionStatusIndicator();
            
            // إضافة إشعارات محسنة
            this.enhanceNotifications();
            
            // إضافة اختصارات لوحة المفاتيح
            this.addKeyboardShortcuts();
            
            console.log('✅ تم إعداد واجهة المستخدم المحسنة');
        } catch (error) {
            console.error('❌ خطأ في إعداد واجهة المستخدم:', error);
        }
    }

    /**
     * إضافة مؤشر حالة الاتصال
     */
    addConnectionStatusIndicator() {
        // إنشاء مؤشر حالة الاتصال
        const statusIndicator = document.createElement('div');
        statusIndicator.id = 'connection-status';
        statusIndicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 9999;
            transition: all 0.3s ease;
        `;
        
        document.body.appendChild(statusIndicator);
        
        // تحديث حالة الاتصال
        this.updateConnectionStatus();
        
        // تحديث دوري لحالة الاتصال
        setInterval(() => {
            this.updateConnectionStatus();
        }, 10000);
    }

    /**
     * تحديث حالة الاتصال
     */
    updateConnectionStatus() {
        const indicator = document.getElementById('connection-status');
        if (!indicator) return;
        
        const isOnline = this.integrationStatus.database && navigator.onLine;
        
        if (isOnline) {
            indicator.textContent = '🟢 متصل';
            indicator.style.backgroundColor = '#d4edda';
            indicator.style.color = '#155724';
        } else {
            indicator.textContent = '🔴 غير متصل';
            indicator.style.backgroundColor = '#f8d7da';
            indicator.style.color = '#721c24';
        }
    }

    /**
     * تحسين الإشعارات
     */
    enhanceNotifications() {
        // إضافة أنواع إشعارات جديدة
        if (!window.showEnhancedToast) {
            window.showEnhancedToast = function(message, type = 'info', duration = 3000) {
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                toast.style.cssText = `
                    position: fixed;
                    top: 50px;
                    right: 20px;
                    padding: 15px 20px;
                    border-radius: 5px;
                    color: white;
                    font-weight: bold;
                    z-index: 10000;
                    animation: slideIn 0.3s ease;
                `;
                
                // تحديد لون الإشعار حسب النوع
                switch (type) {
                    case 'success':
                        toast.style.backgroundColor = '#28a745';
                        break;
                    case 'error':
                        toast.style.backgroundColor = '#dc3545';
                        break;
                    case 'warning':
                        toast.style.backgroundColor = '#ffc107';
                        toast.style.color = '#212529';
                        break;
                    default:
                        toast.style.backgroundColor = '#17a2b8';
                }
                
                toast.textContent = message;
                document.body.appendChild(toast);
                
                // إزالة الإشعار بعد المدة المحددة
                setTimeout(() => {
                    toast.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 300);
                }, duration);
            };
        }
    }

    /**
     * إضافة اختصارات لوحة المفاتيح
     */
    addKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl + S للحفظ
            if (event.ctrlKey && event.key === 's') {
                event.preventDefault();
                if (typeof saveData === 'function') {
                    saveData();
                    window.showEnhancedToast('تم حفظ البيانات', 'success');
                }
            }
            
            // Ctrl + F للبحث
            if (event.ctrlKey && event.key === 'f') {
                event.preventDefault();
                const searchInput = document.querySelector('input[type="search"], input[placeholder*="بحث"]');
                if (searchInput) {
                    searchInput.focus();
                }
            }
            
            // F5 لإعادة التحميل
            if (event.key === 'F5') {
                event.preventDefault();
                location.reload();
            }
        });
    }

    /**
     * الحصول على حالة التكامل
     */
    getIntegrationStatus() {
        return {
            isInitialized: this.isInitialized,
            ...this.integrationStatus
        };
    }
}

// إنشاء مثيل من نظام التكامل
const systemIntegration = new SystemIntegration();

// تصدير النظام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = systemIntegration;
} else {
    window.systemIntegration = systemIntegration;
}
