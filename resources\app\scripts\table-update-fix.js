// إصلاح تحديث الجداول
// Table Update Fix

(function() {
    'use strict';

    // انتظار تحميل النظام
    function waitForSystem() {
        return new Promise((resolve) => {
            const checkSystem = () => {
                if (document.readyState === 'complete') {
                    resolve();
                } else {
                    setTimeout(checkSystem, 100);
                }
            };
            checkSystem();
        });
    }

    // تطبيق إصلاح تحديث الجداول
    async function applyTableUpdateFix() {
        await waitForSystem();

        console.log('🔧 تطبيق إصلاح تحديث الجداول...');

        // إصلاح وظيفة تحديث جدول الإرسال
        fixTransmissionTableUpdate();

        // إصلاح وظيفة تحديث جدول بطاقات الغاز
        fixGasCardsTableUpdate();

        // إصلاح وظيفة تحديث جدول الزبائن
        fixCustomersTableUpdate();

        console.log('✅ تم تطبيق إصلاح تحديث الجداول');
    }

    /**
     * إصلاح وظيفة تحديث جدول الإرسال
     */
    function fixTransmissionTableUpdate() {
        if (typeof window.updateTransmissionTable !== 'function') {
            window.updateTransmissionTable = function() {
                console.log('🔄 تحديث جدول الإرسال...');
                
                try {
                    const tableBody = document.querySelector('#transmission-table tbody');
                    if (!tableBody) {
                        console.log('⚠️ جدول الإرسال غير موجود في الصفحة الحالية');
                        return;
                    }

                    const transmissionData = window.appData?.transmissionTable || [];
                    
                    tableBody.innerHTML = '';
                    
                    if (transmissionData.length === 0) {
                        const emptyRow = document.createElement('tr');
                        emptyRow.innerHTML = '<td colspan="10" style="text-align: center; color: #666;">لا توجد عمليات مسجلة</td>';
                        tableBody.appendChild(emptyRow);
                        return;
                    }

                    transmissionData.forEach((entry, index) => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td><span class="operation-type ${entry.type}">${entry.type}</span></td>
                            <td>${entry.tankNumber || '-'}</td>
                            <td>${entry.carType || '-'}</td>
                            <td>${entry.serialNumber || '-'}</td>
                            <td><strong>${entry.registrationNumber || '-'}</strong></td>
                            <td>${entry.ownerName || '-'}</td>
                            <td>${entry.phoneNumber || '-'}</td>
                            <td>${formatDate(entry.operationDate)}</td>
                            <td><span class="status ${entry.status || 'مكتمل'}">${entry.status || 'مكتمل'}</span></td>
                            <td>
                                <button onclick="editTransmissionEntry('${entry.id}')" class="btn btn-sm btn-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="deleteTransmissionEntry('${entry.id}')" class="btn btn-sm btn-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        `;
                        tableBody.appendChild(row);
                    });

                    // تحديث العداد
                    const countElement = document.querySelector('.transmission-count, #transmission-count');
                    if (countElement) {
                        countElement.textContent = transmissionData.length;
                    }

                    console.log(`✅ تم تحديث جدول الإرسال - ${transmissionData.length} عملية`);

                } catch (error) {
                    console.error('❌ خطأ في تحديث جدول الإرسال:', error);
                }
            };
        }

        console.log('✅ تم إصلاح وظيفة تحديث جدول الإرسال');
    }

    /**
     * إصلاح وظيفة تحديث جدول بطاقات الغاز
     */
    function fixGasCardsTableUpdate() {
        if (typeof window.updateGasCardsTable !== 'function') {
            window.updateGasCardsTable = function() {
                console.log('🔄 تحديث جدول بطاقات الغاز...');
                
                try {
                    const tableBody = document.querySelector('#gas-cards-table tbody');
                    if (!tableBody) {
                        console.log('⚠️ جدول بطاقات الغاز غير موجود في الصفحة الحالية');
                        return;
                    }

                    const gasCardsData = window.appData?.gasCards || [];
                    
                    tableBody.innerHTML = '';
                    
                    if (gasCardsData.length === 0) {
                        const emptyRow = document.createElement('tr');
                        emptyRow.innerHTML = '<td colspan="8" style="text-align: center; color: #666;">لا توجد بطاقات مسجلة</td>';
                        tableBody.appendChild(emptyRow);
                        return;
                    }

                    gasCardsData.forEach((card, index) => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${card.cardNumber || '-'}</td>
                            <td>${card.customerName || '-'}</td>
                            <td>${card.vehicleNumber || '-'}</td>
                            <td>${card.tankNumber || '-'}</td>
                            <td>${formatDate(card.issueDate)}</td>
                            <td>${formatDate(card.expiryDate)}</td>
                            <td><span class="status ${card.status || 'نشطة'}">${card.status || 'نشطة'}</span></td>
                            <td>
                                <button onclick="editGasCard('${card.id}')" class="btn btn-sm btn-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="deleteGasCard('${card.id}')" class="btn btn-sm btn-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <button onclick="printGasCard('${card.id}')" class="btn btn-sm btn-info" title="طباعة">
                                    <i class="fas fa-print"></i>
                                </button>
                            </td>
                        `;
                        tableBody.appendChild(row);
                    });

                    // تحديث العداد
                    const countElement = document.querySelector('.gas-cards-count, #gas-cards-count');
                    if (countElement) {
                        countElement.textContent = gasCardsData.length;
                    }

                    console.log(`✅ تم تحديث جدول بطاقات الغاز - ${gasCardsData.length} بطاقة`);

                } catch (error) {
                    console.error('❌ خطأ في تحديث جدول بطاقات الغاز:', error);
                }
            };
        }

        console.log('✅ تم إصلاح وظيفة تحديث جدول بطاقات الغاز');
    }

    /**
     * إصلاح وظيفة تحديث جدول الزبائن
     */
    function fixCustomersTableUpdate() {
        // تحسين وظيفة تحديث جدول الزبائن الموجودة
        const originalUpdateCustomersTable = window.updateCustomersTable;
        
        window.updateCustomersTable = function() {
            try {
                // استدعاء الوظيفة الأصلية إذا كانت موجودة
                if (originalUpdateCustomersTable && typeof originalUpdateCustomersTable === 'function') {
                    originalUpdateCustomersTable.call(this);
                }

                // تحديث إضافي للتأكد
                const tableBody = document.querySelector('#customers-table tbody');
                if (tableBody && window.appData?.customers) {
                    const customers = window.appData.customers;
                    
                    // تحديث العداد
                    const countElement = document.querySelector('.customers-count, #customers-count');
                    if (countElement) {
                        countElement.textContent = customers.length;
                    }

                    console.log(`✅ تم تحديث جدول الزبائن - ${customers.length} زبون`);
                }

            } catch (error) {
                console.error('❌ خطأ في تحديث جدول الزبائن:', error);
            }
        };

        console.log('✅ تم تحسين وظيفة تحديث جدول الزبائن');
    }

    /**
     * تنسيق التاريخ
     */
    function formatDate(dateString) {
        if (!dateString) return '-';
        
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '-';
            
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        } catch (error) {
            return '-';
        }
    }

    /**
     * إضافة وظائف مساعدة للتعديل والحذف
     */
    function addHelperFunctions() {
        // وظيفة تعديل عملية إرسال
        if (typeof window.editTransmissionEntry !== 'function') {
            window.editTransmissionEntry = function(entryId) {
                console.log('تعديل عملية الإرسال:', entryId);
                // يمكن تنفيذ منطق التعديل هنا
            };
        }

        // وظيفة حذف عملية إرسال
        if (typeof window.deleteTransmissionEntry !== 'function') {
            window.deleteTransmissionEntry = function(entryId) {
                if (confirm('هل أنت متأكد من حذف هذه العملية؟')) {
                    if (window.appData?.transmissionTable) {
                        window.appData.transmissionTable = window.appData.transmissionTable.filter(entry => entry.id !== entryId);
                        if (typeof saveData === 'function') {
                            saveData();
                        }
                        window.updateTransmissionTable();
                        console.log('تم حذف عملية الإرسال:', entryId);
                    }
                }
            };
        }

        // وظيفة تعديل بطاقة غاز
        if (typeof window.editGasCard !== 'function') {
            window.editGasCard = function(cardId) {
                console.log('تعديل بطاقة الغاز:', cardId);
                // يمكن تنفيذ منطق التعديل هنا
            };
        }

        // وظيفة حذف بطاقة غاز
        if (typeof window.deleteGasCard !== 'function') {
            window.deleteGasCard = function(cardId) {
                if (confirm('هل أنت متأكد من حذف هذه البطاقة؟')) {
                    if (window.appData?.gasCards) {
                        window.appData.gasCards = window.appData.gasCards.filter(card => card.id !== cardId);
                        if (typeof saveData === 'function') {
                            saveData();
                        }
                        window.updateGasCardsTable();
                        console.log('تم حذف بطاقة الغاز:', cardId);
                    }
                }
            };
        }

        // وظيفة طباعة بطاقة غاز
        if (typeof window.printGasCard !== 'function') {
            window.printGasCard = function(cardId) {
                console.log('طباعة بطاقة الغاز:', cardId);
                // يمكن تنفيذ منطق الطباعة هنا
            };
        }

        console.log('✅ تم إضافة الوظائف المساعدة');
    }

    // تطبيق الإصلاح عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                applyTableUpdateFix();
                addHelperFunctions();
            }, 1000);
        });
    } else {
        setTimeout(() => {
            applyTableUpdateFix();
            addHelperFunctions();
        }, 1000);
    }

    // تصدير الوظائف للاستخدام العام
    window.tableUpdateFix = {
        applyTableUpdateFix,
        fixTransmissionTableUpdate,
        fixGasCardsTableUpdate,
        fixCustomersTableUpdate
    };

})();
