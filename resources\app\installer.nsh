; إعدادات مخصصة للمثبت NSIS
; Custom NSIS installer settings

; إضافة اختصارات إضافية
!macro customInstall
  ; إنشاء اختصار سطح المكتب مع أيقونة مخصصة
  CreateShortCut "$DESKTOP\مؤسسة وقود المستقبل.lnk" "$INSTDIR\${APP_EXECUTABLE_FILENAME}" "" "$INSTDIR\resources\app\icons\app-icon.ico" 0 SW_SHOWNORMAL "" "نظام إدارة مؤسسة وقود المستقبل"
  
  ; إنشاء اختصار في قائمة ابدأ
  CreateDirectory "$SMPROGRAMS\مؤسسة وقود المستقبل"
  CreateShortCut "$SMPROGRAMS\مؤسسة وقود المستقبل\مؤسسة وقود المستقبل.lnk" "$INSTDIR\${APP_EXECUTABLE_FILENAME}" "" "$INSTDIR\resources\app\icons\app-icon.ico" 0 SW_SHOWNORMAL "" "نظام إدارة مؤسسة وقود المستقبل"
  CreateShortCut "$SMPROGRAMS\مؤسسة وقود المستقبل\إلغاء التثبيت.lnk" "$INSTDIR\Uninstall ${APP_EXECUTABLE_FILENAME}.exe"
  
  ; إنشاء اختصار في شريط المهام (Windows 7+)
  ${If} ${AtLeastWin7}
    ; تثبيت في شريط المهام
    ExecShell "taskbarpin" "$INSTDIR\${APP_EXECUTABLE_FILENAME}"
  ${EndIf}
  
  ; تسجيل التطبيق في النظام
  WriteRegStr HKLM "SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\${APP_EXECUTABLE_FILENAME}" "" "$INSTDIR\${APP_EXECUTABLE_FILENAME}"
  WriteRegStr HKLM "SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\${APP_EXECUTABLE_FILENAME}" "Path" "$INSTDIR"
  
  ; إضافة إلى قائمة البرامج المثبتة
  WriteRegStr HKLM "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "DisplayName" "مؤسسة وقود المستقبل"
  WriteRegStr HKLM "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "DisplayIcon" "$INSTDIR\resources\app\icons\app-icon.ico"
  WriteRegStr HKLM "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "Publisher" "Future Fuel Corporation"
  WriteRegStr HKLM "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "URLInfoAbout" "https://github.com/future-fuel/gas-shop-management"
  WriteRegStr HKLM "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "HelpLink" "https://github.com/future-fuel/gas-shop-management/issues"
  
  ; ربط أنواع الملفات (اختياري)
  WriteRegStr HKCR ".fuelfuel" "" "FutureFuelData"
  WriteRegStr HKCR "FutureFuelData" "" "ملف بيانات مؤسسة وقود المستقبل"
  WriteRegStr HKCR "FutureFuelData\DefaultIcon" "" "$INSTDIR\resources\app\icons\app-icon.ico"
  WriteRegStr HKCR "FutureFuelData\shell\open\command" "" '"$INSTDIR\${APP_EXECUTABLE_FILENAME}" "%1"'
!macroend

; إعدادات إلغاء التثبيت
!macro customUnInstall
  ; حذف الاختصارات
  Delete "$DESKTOP\مؤسسة وقود المستقبل.lnk"
  Delete "$SMPROGRAMS\مؤسسة وقود المستقبل\مؤسسة وقود المستقبل.lnk"
  Delete "$SMPROGRAMS\مؤسسة وقود المستقبل\إلغاء التثبيت.lnk"
  RMDir "$SMPROGRAMS\مؤسسة وقود المستقبل"
  
  ; إلغاء تثبيت من شريط المهام
  ${If} ${AtLeastWin7}
    ExecShell "taskbarunpin" "$INSTDIR\${APP_EXECUTABLE_FILENAME}"
  ${EndIf}
  
  ; حذف مفاتيح التسجيل
  DeleteRegKey HKLM "SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\${APP_EXECUTABLE_FILENAME}"
  DeleteRegKey HKLM "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}"
  DeleteRegKey HKCR ".fuelfuel"
  DeleteRegKey HKCR "FutureFuelData"
  
  ; حذف بيانات المستخدم (اختياري - يمكن تعطيله)
  ; RMDir /r "$APPDATA\مؤسسة وقود المستقبل"
!macroend

; رسائل مخصصة
!macro customHeader
  !define MUI_WELCOMEPAGE_TITLE "مرحباً بك في مثبت مؤسسة وقود المستقبل"
  !define MUI_WELCOMEPAGE_TEXT "سيقوم هذا المعالج بتثبيت نظام إدارة مؤسسة وقود المستقبل على جهازك.$\r$\n$\r$\nيُنصح بإغلاق جميع التطبيقات الأخرى قبل المتابعة.$\r$\n$\r$\nانقر على 'التالي' للمتابعة."
  !define MUI_FINISHPAGE_TITLE "تم تثبيت مؤسسة وقود المستقبل بنجاح"
  !define MUI_FINISHPAGE_TEXT "تم تثبيت نظام إدارة مؤسسة وقود المستقبل بنجاح على جهازك.$\r$\n$\r$\nيمكنك الآن تشغيل التطبيق من سطح المكتب أو قائمة ابدأ."
  !define MUI_FINISHPAGE_RUN_TEXT "تشغيل مؤسسة وقود المستقبل الآن"
!macroend

; إعدادات إضافية للواجهة
!macro customInit
  ; فحص إصدار Windows
  ${IfNot} ${AtLeastWin7}
    MessageBox MB_OK|MB_ICONSTOP "هذا التطبيق يتطلب Windows 7 أو أحدث."
    Abort
  ${EndIf}
  
  ; فحص وجود .NET Framework (إذا كان مطلوباً)
  ; يمكن إضافة فحص إضافي هنا
!macroend
