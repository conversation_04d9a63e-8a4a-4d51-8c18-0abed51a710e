# مؤسسة وقود المستقبل - Electron Application

## 🚀 تشغيل التطبيق (Running the Application)

### 1. تثبيت التبعيات (Install Dependencies)

**الطريقة السهلة (مستحسنة):**
```cmd
install-electron.bat
```

**أو باستخدام npm مباشرة:**
```bash
npm install
npm install electron --save-dev
```

**أو PowerShell:**
```powershell
.\install-electron.ps1
```

### 2. تشغيل التطبيق (Start Application)

**تشغيل سريع:**
```cmd
run-app.bat
```

**أو باستخدام npm:**
```bash
# تشغيل عادي
npm start

# تشغيل في وضع التطوير
npm run dev

# تشغيل كصفحة ويب
npm run web
```

### 3. بناء التطبيق (Build Application)

**بناء تفاعلي:**
```cmd
build-app.bat
```

**أو باستخدام npm:**
```bash
# بناء للويندوز (كلا الإصدارين)
npm run build-win

# بناء 32-bit فقط
npm run build-win32

# بناء 64-bit فقط
npm run build-win64

# إنشاء حزمة فقط (بدون مثبت)
npm run pack
```

## 🌟 الميزات المتوفرة (Available Features)

### ✅ واجهة سطح المكتب المتقدمة
- نافذة تطبيق مستقلة مع تحكم كامل
- قوائم عربية متكاملة مع اختصارات
- اختصارات لوحة المفاتيح محسنة
- حفظ وطباعة متقدم
- دعم الوضع المظلم والفاتح
- واجهة مستجيبة وسريعة

### ✅ الأمان المتقدم
- عزل السياق (Context Isolation) كامل
- عدم تكامل Node.js مباشر للحماية
- APIs آمنة عبر preload script محسن
- حماية من XSS و CSRF
- تشفير البيانات المحلية
- فلترة المحتوى الخارجي

### ✅ إدارة البيانات الذكية
- حفظ تلقائي مع كشف التغييرات
- نظام نسخ احتياطية متقدم
- تصدير واستيراد بصيغ متعددة
- ضغط البيانات لتوفير المساحة
- استعادة النسخ الاحتياطية بسهولة
- مزامنة البيانات

### ✅ التحديث التلقائي
- فحص التحديثات تلقائياً
- تنزيل وتثبيت آمن
- إشعارات التحديث
- نسخ احتياطية قبل التحديث
- استعادة في حالة الفشل

### ✅ البناء والتوزيع المتقدم
- مثبت NSIS مخصص
- إصدار محمول مستقل
- دعم 32-bit و 64-bit
- ضغط متقدم للملفات
- توقيع رقمي للأمان
- تثبيت صامت

## حل مشاكل PowerShell (PowerShell Issues)

إذا واجهت مشكلة في PowerShell:

```powershell
# تشغيل كمدير وتنفيذ:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

أو استخدم Command Prompt بدلاً من PowerShell.

## هيكل المشروع (Project Structure)

```
HR/
├── main.js              # العملية الرئيسية
├── preload.js           # سكريبت التحميل المسبق
├── index.html           # الواجهة الرئيسية
├── package.json         # إعدادات المشروع
├── scripts/             # سكريبتات JavaScript
├── styles/              # ملفات CSS
├── icons/               # أيقونات التطبيق
└── install-electron.*   # ملفات التثبيت المساعدة
```

## الدعم الفني (Technical Support)

- **الإصدار الحالي:** 2.2.0
- **Electron:** v32.0.0
- **Node.js:** مطلوب v16 أو أحدث
- **نظام التشغيل:** Windows 7/8/10/11

## 🖥️ تثبيت التطبيق على سطح المكتب (Desktop Installation)

### **الطريقة السريعة (مستحسنة):**
```cmd
install-to-desktop.bat
```

### **إنشاء اختصار فقط:**
```cmd
create-desktop-shortcut.bat
```

### **باستخدام PowerShell:**
```powershell
.\Install-DesktopApp.ps1
```

### **خيارات التثبيت المتقدمة:**

#### 1. **اختصار سريع:**
- شغل `create-desktop-shortcut.bat`
- سيتم إنشاء أيقونة على سطح المكتب فوراً

#### 2. **تثبيت كامل:**
- شغل `install-to-desktop.bat`
- اختر "تثبيت شامل (جميع الخيارات)"
- سيتم نسخ التطبيق لمجلد البرامج وإنشاء اختصارات

#### 3. **تثبيت مخصص:**
```powershell
.\Install-DesktopApp.ps1 -FullInstall -Silent
```

### **بعد التثبيت:**
- ✅ أيقونة على سطح المكتب
- ✅ اختصار في قائمة ابدأ
- ✅ تشغيل بنقرة واحدة
- ✅ أيقونة مخصصة عربية

## ملاحظات مهمة (Important Notes)

1. تأكد من تثبيت Node.js قبل تشغيل التطبيق
2. استخدم `install-electron.bat` إذا واجهت مشاكل في PowerShell
3. التطبيق يدعم اللغة العربية بالكامل
4. جميع البيانات محفوظة محلياً وآمنة
5. الأيقونات تدعم الدقة العالية (High DPI)
6. يمكن تشغيل التطبيق بدون إنترنت
