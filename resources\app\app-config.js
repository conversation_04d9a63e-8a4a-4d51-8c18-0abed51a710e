// إعدادات التطبيق المتقدمة
const path = require('path');
const { app } = require('electron');

const isDev = process.env.NODE_ENV === 'development';

const config = {
  // إعدادات النافذة
  window: {
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    center: true,
    resizable: true,
    maximizable: true,
    minimizable: true,
    closable: true,
    alwaysOnTop: false,
    skipTaskbar: false,
    kiosk: false,
    fullscreen: false,
    simpleFullscreen: false,
    titleBarStyle: 'default',
    frame: true,
    transparent: false,
    opacity: 1.0,
    hasShadow: true,
    thickFrame: true,
    vibrancy: null,
    zoomToPageWidth: false,
    tabbingIdentifier: null,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      scrollBounce: false,
      enableBlinkFeatures: '',
      disableBlinkFeatures: '',
      defaultFontFamily: {
        standard: 'Arial',
        serif: 'Times New Roman',
        sansSerif: 'Arial',
        monospace: 'Courier New',
        cursive: 'Script',
        fantasy: 'Impact'
      },
      defaultFontSize: 16,
      defaultMonospaceFontSize: 13,
      minimumFontSize: 0,
      defaultEncoding: 'UTF-8',
      backgroundThrottling: true,
      offscreen: false,
      sandbox: false,
      session: null,
      partition: null,
      zoomFactor: 1.0,
      javascript: true,
      webgl: true,
      webaudio: true,
      plugins: false,
      experimentalCanvasFeatures: false,
      navigateOnDragDrop: false,
      autoplayPolicy: 'user-gesture-required',
      disableHtmlFullscreenWindowResize: false
    }
  },

  // إعدادات التطبيق
  app: {
    name: 'مؤسسة وقود المستقبل',
    version: '2.2.0',
    description: 'نظام إدارة مؤسسة وقود المستقبل',
    author: 'Future Fuel Corporation',
    homepage: 'https://github.com/future-fuel/gas-shop-management',
    license: 'MIT',
    copyright: 'Copyright © 2024 Future Fuel Corporation',
    
    // مسارات الملفات
    paths: {
      userData: app.getPath('userData'),
      documents: app.getPath('documents'),
      downloads: app.getPath('downloads'),
      desktop: app.getPath('desktop'),
      pictures: app.getPath('pictures'),
      temp: app.getPath('temp'),
      appData: app.getPath('appData'),
      exe: app.getPath('exe'),
      module: app.getPath('module'),
      logs: app.getPath('logs')
    },

    // إعدادات الأمان
    security: {
      allowedHosts: [
        'localhost',
        '127.0.0.1',
        'cdnjs.cloudflare.com',
        'cdn.jsdelivr.net',
        'fonts.googleapis.com',
        'fonts.gstatic.com'
      ],
      csp: {
        'default-src': "'self'",
        'script-src': "'self' 'unsafe-inline' cdnjs.cloudflare.com cdn.jsdelivr.net",
        'style-src': "'self' 'unsafe-inline' fonts.googleapis.com cdnjs.cloudflare.com",
        'font-src': "'self' fonts.gstatic.com cdnjs.cloudflare.com",
        'img-src': "'self' data: blob:",
        'connect-src': "'self'",
        'media-src': "'self'",
        'object-src': "'none'",
        'child-src': "'none'",
        'worker-src': "'none'",
        'frame-src': "'none'",
        'base-uri': "'self'",
        'form-action': "'self'"
      }
    },

    // إعدادات التحديث
    updater: {
      enabled: !isDev,
      autoDownload: true,
      autoInstallOnAppQuit: true,
      allowPrerelease: false,
      allowDowngrade: false,
      requestHeaders: {},
      timeout: 120000
    },

    // إعدادات السجلات
    logging: {
      level: isDev ? 'debug' : 'info',
      format: '[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}] {text}',
      file: {
        level: 'info',
        format: '[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}] {text}',
        maxSize: 10 * 1024 * 1024, // 10MB
        archiveLog: true
      },
      console: {
        level: isDev ? 'debug' : 'warn',
        format: '[{h}:{i}:{s}.{ms}] [{level}] {text}'
      }
    },

    // إعدادات النسخ الاحتياطية
    backup: {
      enabled: true,
      interval: 30 * 60 * 1000, // 30 دقيقة
      maxBackups: 10,
      compression: true,
      location: path.join(app.getPath('userData'), 'backups')
    },

    // إعدادات الإشعارات
    notifications: {
      enabled: true,
      sound: true,
      badge: true,
      silent: false,
      urgency: 'normal'
    },

    // إعدادات الواجهة
    ui: {
      theme: 'auto', // 'light', 'dark', 'auto'
      language: 'ar',
      direction: 'rtl',
      animations: true,
      transitions: true,
      sounds: true,
      tooltips: true,
      confirmations: true
    },

    // إعدادات الأداء
    performance: {
      hardwareAcceleration: true,
      backgroundThrottling: true,
      v8CacheOptions: 'code',
      enableLargeObjects: false
    }
  },

  // إعدادات التطوير
  development: {
    devTools: isDev,
    reload: isDev,
    errorReporting: isDev,
    debugging: isDev,
    profiling: false,
    memoryUsage: isDev
  }
};

module.exports = config;
