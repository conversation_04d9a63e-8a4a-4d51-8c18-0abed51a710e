// الإصلاح النهائي لنظام العمليات
// Final Operation System Fix

(function() {
    'use strict';

    // انتظار تحميل جميع الأنظمة
    function waitForAllSystems() {
        return new Promise((resolve) => {
            const checkSystems = () => {
                if (document.readyState === 'complete' && 
                    typeof window.appData !== 'undefined' &&
                    document.getElementById('customer-form')) {
                    resolve();
                } else {
                    setTimeout(checkSystems, 200);
                }
            };
            checkSystems();
        });
    }

    // تطبيق الإصلاح النهائي
    async function applyFinalFix() {
        await waitForAllSystems();

        console.log('🔧 تطبيق الإصلاح النهائي لنظام العمليات...');

        // التأكد من وجود البيانات الأساسية
        ensureDataStructure();

        // إصلاح معالج النموذج
        fixFormHandler();

        // إضافة مراقب للعمليات
        addOperationMonitor();

        // إضافة إشعارات محسنة
        addEnhancedNotifications();

        console.log('✅ تم تطبيق الإصلاح النهائي لنظام العمليات');
    }

    /**
     * التأكد من وجود هيكل البيانات
     */
    function ensureDataStructure() {
        if (!window.appData) {
            window.appData = {};
        }

        // التأكد من وجود المصفوفات الأساسية
        const requiredArrays = ['customers', 'vehicles', 'gasTanks', 'gasCards', 'transmissionTable'];
        
        requiredArrays.forEach(arrayName => {
            if (!Array.isArray(window.appData[arrayName])) {
                window.appData[arrayName] = [];
                console.log(`✅ تم إنشاء مصفوفة ${arrayName}`);
            }
        });

        console.log('✅ تم التأكد من هيكل البيانات');
    }

    /**
     * إصلاح معالج النموذج
     */
    function fixFormHandler() {
        const customerForm = document.getElementById('customer-form');
        if (!customerForm) return;

        // إضافة معالج إضافي للتأكد من العمليات
        customerForm.addEventListener('submit', function(e) {
            // انتظار قليل للسماح للمعالج الأصلي بالعمل
            setTimeout(() => {
                handleOperationAfterSubmit();
            }, 500);
        });

        console.log('✅ تم إصلاح معالج النموذج');
    }

    /**
     * معالجة العملية بعد إرسال النموذج
     */
    function handleOperationAfterSubmit() {
        try {
            const operationType = document.getElementById('operation-type')?.value;
            if (!operationType) return;

            const operationDate = document.getElementById('operation-date')?.value || new Date().toISOString().split('T')[0];
            const operationNotes = document.getElementById('operation-notes')?.value || '';

            // جمع البيانات من النموذج
            const customerName = document.getElementById('customer-name')?.value;
            const customerPhone = document.getElementById('customer-phone')?.value;
            const plateNumber = document.getElementById('vehicle-plate-number')?.value;
            const vehicleBrand = document.getElementById('vehicle-brand')?.value;
            const vehicleModel = document.getElementById('vehicle-model')?.value;
            const tankSerialNumber = document.getElementById('tank-serial-number')?.value;

            if (!customerName || !plateNumber) {
                console.log('⚠️ بيانات غير مكتملة للعملية');
                return;
            }

            // معالجة العملية حسب النوع
            switch (operationType) {
                case 'تركيب':
                case 'مراقبة':
                    addToTransmissionTableFinal({
                        type: operationType,
                        customerName,
                        customerPhone,
                        plateNumber,
                        vehicleBrand,
                        vehicleModel,
                        tankSerialNumber,
                        operationDate,
                        operationNotes
                    });
                    break;

                case 'تجديد':
                    addToGasCardsFinal({
                        customerName,
                        customerPhone,
                        plateNumber,
                        tankSerialNumber,
                        operationDate,
                        operationNotes
                    });
                    break;
            }

        } catch (error) {
            console.error('❌ خطأ في معالجة العملية:', error);
        }
    }

    /**
     * إضافة إلى جدول الإرسال (نهائي)
     */
    function addToTransmissionTableFinal(data) {
        try {
            const entry = {
                id: 'trans-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
                type: data.type,
                tankNumber: data.tankSerialNumber || '',
                carType: `${data.vehicleBrand || ''} ${data.vehicleModel || ''}`.trim(),
                serialNumber: data.tankSerialNumber || '',
                registrationNumber: data.plateNumber || '',
                ownerName: data.customerName || '',
                phoneNumber: data.customerPhone || '',
                operationDate: data.operationDate,
                notes: data.operationNotes,
                status: 'مكتمل',
                source: 'customer_form_final',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // إضافة إلى البيانات
            window.appData.transmissionTable.push(entry);

            // حفظ البيانات
            if (typeof saveData === 'function') {
                saveData();
            }

            // تحديث الجدول
            if (typeof updateTransmissionTable === 'function') {
                updateTransmissionTable();
            }

            // إظهار إشعار
            showOperationSuccess(`تم إضافة عملية ${data.type} إلى جدول الإرسال بنجاح`);

            console.log(`✅ تم إضافة عملية ${data.type} إلى جدول الإرسال:`, entry);

        } catch (error) {
            console.error('❌ خطأ في إضافة العملية إلى جدول الإرسال:', error);
            showOperationError('فشل في إضافة العملية إلى جدول الإرسال');
        }
    }

    /**
     * إضافة إلى بطاقات الغاز (نهائي)
     */
    function addToGasCardsFinal(data) {
        try {
            const cardNumber = generateCardNumber();
            const card = {
                id: 'card-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
                cardNumber: cardNumber,
                customerName: data.customerName || '',
                vehicleNumber: data.plateNumber || '',
                tankNumber: data.tankSerialNumber || '',
                serialNumber: data.tankSerialNumber || '',
                issueDate: data.operationDate,
                expiryDate: calculateExpiryDate(data.operationDate),
                notes: data.operationNotes,
                status: 'نشطة',
                operationType: 'تجديد بطاقة',
                operationDate: data.operationDate,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // إضافة إلى البيانات
            window.appData.gasCards.push(card);

            // حفظ البيانات
            if (typeof saveData === 'function') {
                saveData();
            }

            // تحديث الجدول
            if (typeof updateGasCardsTable === 'function') {
                updateGasCardsTable();
            }

            // إظهار إشعار
            showOperationSuccess(`تم إضافة بطاقة غاز جديدة برقم ${cardNumber} بنجاح`);

            console.log('✅ تم إضافة بطاقة غاز جديدة:', card);

        } catch (error) {
            console.error('❌ خطأ في إضافة بطاقة الغاز:', error);
            showOperationError('فشل في إضافة بطاقة الغاز');
        }
    }

    /**
     * توليد رقم بطاقة غاز
     */
    function generateCardNumber() {
        const year = new Date().getFullYear();
        const month = String(new Date().getMonth() + 1).padStart(2, '0');
        const day = String(new Date().getDate()).padStart(2, '0');
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        return `GC-${year}${month}${day}-${random}`;
    }

    /**
     * حساب تاريخ انتهاء البطاقة
     */
    function calculateExpiryDate(issueDate) {
        const issue = new Date(issueDate);
        const expiry = new Date(issue);
        expiry.setFullYear(expiry.getFullYear() + 1);
        return expiry.toISOString().split('T')[0];
    }

    /**
     * إضافة مراقب للعمليات
     */
    function addOperationMonitor() {
        // مراقبة تغيير نوع العملية
        const operationTypeSelect = document.getElementById('operation-type');
        if (operationTypeSelect) {
            operationTypeSelect.addEventListener('change', function(e) {
                updateOperationLabels(e.target.value);
                showOperationInfo(e.target.value);
            });
        }

        console.log('✅ تم إضافة مراقب العمليات');
    }

    /**
     * تحديث تسميات العملية
     */
    function updateOperationLabels(operationType) {
        const operationDateLabel = document.getElementById('operation-date-label');
        const operationNotesLabel = document.querySelector('label[for="operation-notes"]');

        if (operationDateLabel) {
            switch (operationType) {
                case 'تركيب':
                    operationDateLabel.textContent = 'تاريخ التركيب:';
                    break;
                case 'مراقبة':
                    operationDateLabel.textContent = 'تاريخ المراقبة:';
                    break;
                case 'تجديد':
                    operationDateLabel.textContent = 'تاريخ التجديد:';
                    break;
                default:
                    operationDateLabel.textContent = 'تاريخ العملية:';
            }
        }

        if (operationNotesLabel) {
            switch (operationType) {
                case 'تركيب':
                    operationNotesLabel.textContent = 'ملاحظات التركيب:';
                    break;
                case 'مراقبة':
                    operationNotesLabel.textContent = 'ملاحظات المراقبة:';
                    break;
                case 'تجديد':
                    operationNotesLabel.textContent = 'ملاحظات التجديد:';
                    break;
                default:
                    operationNotesLabel.textContent = 'ملاحظات:';
            }
        }
    }

    /**
     * إظهار معلومات العملية
     */
    function showOperationInfo(operationType) {
        let message = '';
        switch (operationType) {
            case 'تركيب':
                message = 'سيتم حفظ عملية التركيب في جدول الإرسال';
                break;
            case 'مراقبة':
                message = 'سيتم حفظ عملية المراقبة في جدول الإرسال';
                break;
            case 'تجديد':
                message = 'سيتم إنشاء بطاقة غاز جديدة في إدارة بطاقات الغاز';
                break;
        }

        if (message) {
            showOperationInfo_Display(message);
        }
    }

    /**
     * إضافة إشعارات محسنة
     */
    function addEnhancedNotifications() {
        // إنشاء منطقة الإشعارات إذا لم تكن موجودة
        if (!document.getElementById('operation-notifications')) {
            const notificationArea = document.createElement('div');
            notificationArea.id = 'operation-notifications';
            notificationArea.style.cssText = `
                position: fixed;
                top: 70px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
            `;
            document.body.appendChild(notificationArea);
        }

        console.log('✅ تم إضافة نظام الإشعارات المحسن');
    }

    /**
     * إظهار إشعار نجاح العملية
     */
    function showOperationSuccess(message) {
        if (window.showEnhancedToast) {
            window.showEnhancedToast(message, 'success', 5000);
        } else if (window.showToast) {
            window.showToast(message, true);
        } else {
            showCustomNotification(message, 'success');
        }
    }

    /**
     * إظهار إشعار خطأ العملية
     */
    function showOperationError(message) {
        if (window.showEnhancedToast) {
            window.showEnhancedToast(message, 'error', 5000);
        } else if (window.showToast) {
            window.showToast(message, false);
        } else {
            showCustomNotification(message, 'error');
        }
    }

    /**
     * إظهار معلومات العملية
     */
    function showOperationInfo_Display(message) {
        if (window.showEnhancedToast) {
            window.showEnhancedToast(message, 'info', 3000);
        } else {
            showCustomNotification(message, 'info');
        }
    }

    /**
     * إظهار إشعار مخصص
     */
    function showCustomNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `operation-notification ${type}`;
        notification.style.cssText = `
            background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
            color: white;
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            animation: slideInRight 0.3s ease;
        `;
        notification.textContent = message;

        const notificationArea = document.getElementById('operation-notifications');
        if (notificationArea) {
            notificationArea.appendChild(notification);

            // إزالة الإشعار بعد 5 ثوان
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        }
    }

    // إضافة أنماط CSS للإشعارات
    function addNotificationStyles() {
        if (document.getElementById('operation-notification-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'operation-notification-styles';
        styles.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }

            .operation-notification {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                font-size: 14px;
                line-height: 1.4;
            }
        `;
        document.head.appendChild(styles);
    }

    // تطبيق الإصلاح عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                addNotificationStyles();
                applyFinalFix();
            }, 2000);
        });
    } else {
        setTimeout(() => {
            addNotificationStyles();
            applyFinalFix();
        }, 2000);
    }

    // تصدير الوظائف للاستخدام العام
    window.finalOperationFix = {
        applyFinalFix,
        addToTransmissionTableFinal,
        addToGasCardsFinal,
        showOperationSuccess,
        showOperationError
    };

})();
