// ملف اختبار الوضع المظلم
// يمكن تشغيل هذا الملف في وحدة التحكم لاختبار الوضع المظلم

console.log('🧪 بدء اختبار الوضع المظلم...');

// اختبار شامل للوضع المظلم
function runDarkModeTests() {
    const tests = [];
    
    // اختبار 1: وجود زر الوضع المظلم
    tests.push({
        name: 'وجود زر الوضع المظلم',
        test: () => !!document.getElementById('dark-mode-toggle'),
        expected: true
    });
    
    // اختبار 2: وجود أنماط CSS للوضع المظلم
    tests.push({
        name: 'وجود أنماط CSS للوضع المظلم',
        test: () => {
            const styles = getComputedStyle(document.documentElement);
            return styles.getPropertyValue('--bg-color').trim() !== '';
        },
        expected: true
    });
    
    // اختبار 3: تفعيل الوضع المظلم
    tests.push({
        name: 'تفعيل الوضع المظلم',
        test: () => {
            enableDarkMode();
            return document.body.classList.contains('dark-mode');
        },
        expected: true
    });
    
    // اختبار 4: حفظ الإعداد في localStorage
    tests.push({
        name: 'حفظ الإعداد في localStorage',
        test: () => {
            enableDarkMode();
            return localStorage.getItem('theme') === 'dark';
        },
        expected: true
    });
    
    // اختبار 5: تغيير أيقونة الزر
    tests.push({
        name: 'تغيير أيقونة الزر',
        test: () => {
            enableDarkMode();
            const toggle = document.getElementById('dark-mode-toggle');
            const icon = toggle?.querySelector('i');
            return icon?.className.includes('fa-sun');
        },
        expected: true
    });
    
    // اختبار 6: تفعيل الوضع الفاتح
    tests.push({
        name: 'تفعيل الوضع الفاتح',
        test: () => {
            enableLightMode();
            return !document.body.classList.contains('dark-mode');
        },
        expected: true
    });
    
    // اختبار 7: تبديل الوضع
    tests.push({
        name: 'تبديل الوضع',
        test: () => {
            const initialMode = document.body.classList.contains('dark-mode');
            const toggle = document.getElementById('dark-mode-toggle');
            toggle?.click();
            const newMode = document.body.classList.contains('dark-mode');
            return initialMode !== newMode;
        },
        expected: true
    });
    
    // تشغيل الاختبارات
    console.log('🔬 تشغيل الاختبارات...');
    let passed = 0;
    let failed = 0;
    
    tests.forEach((test, index) => {
        try {
            const result = test.test();
            const success = result === test.expected;
            
            if (success) {
                console.log(`✅ اختبار ${index + 1}: ${test.name} - نجح`);
                passed++;
            } else {
                console.error(`❌ اختبار ${index + 1}: ${test.name} - فشل (النتيجة: ${result}, المتوقع: ${test.expected})`);
                failed++;
            }
        } catch (error) {
            console.error(`💥 اختبار ${index + 1}: ${test.name} - خطأ: ${error.message}`);
            failed++;
        }
    });
    
    // النتائج النهائية
    console.log('\n📊 نتائج الاختبار:');
    console.log(`✅ نجح: ${passed}`);
    console.log(`❌ فشل: ${failed}`);
    console.log(`📈 معدل النجاح: ${((passed / tests.length) * 100).toFixed(1)}%`);
    
    if (failed === 0) {
        console.log('🎉 جميع الاختبارات نجحت! الوضع المظلم يعمل بشكل صحيح.');
    } else {
        console.log('⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
    }
    
    return { passed, failed, total: tests.length };
}

// اختبار سريع للوضع المظلم
function quickDarkModeTest() {
    console.log('⚡ اختبار سريع للوضع المظلم...');
    
    const toggle = document.getElementById('dark-mode-toggle');
    if (!toggle) {
        console.error('❌ زر الوضع المظلم غير موجود');
        return false;
    }
    
    const initialMode = document.body.classList.contains('dark-mode');
    console.log(`🔍 الوضع الحالي: ${initialMode ? 'مظلم' : 'فاتح'}`);
    
    // تجربة التبديل
    toggle.click();
    
    setTimeout(() => {
        const newMode = document.body.classList.contains('dark-mode');
        console.log(`🔄 الوضع بعد التبديل: ${newMode ? 'مظلم' : 'فاتح'}`);
        
        if (initialMode !== newMode) {
            console.log('✅ الوضع المظلم يعمل بشكل صحيح!');
        } else {
            console.error('❌ فشل في تبديل الوضع');
        }
    }, 100);
    
    return true;
}

// إضافة الدوال إلى النطاق العام
window.darkModeTests = {
    runAll: runDarkModeTests,
    quick: quickDarkModeTest
};

console.log('✅ تم تحميل اختبارات الوضع المظلم');
console.log('💡 استخدم darkModeTests.runAll() لتشغيل جميع الاختبارات');
console.log('💡 استخدم darkModeTests.quick() للاختبار السريع');
