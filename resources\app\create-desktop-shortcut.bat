@echo off
chcp 65001 >nul
title إنشاء اختصار سطح المكتب - مؤسسة وقود المستقبل

echo ========================================
echo    إنشاء اختصار سطح المكتب
echo    مؤسسة وقود المستقبل
echo ========================================
echo.

REM Get current directory
set "CURRENT_DIR=%cd%"

REM Get desktop path
for /f "tokens=3*" %%i in ('reg query "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\User Shell Folders" /v Desktop 2^>nul') do set "DESKTOP_PATH=%%j"
if "%DESKTOP_PATH%"=="" set "DESKTOP_PATH=%USERPROFILE%\Desktop"

REM Expand environment variables in desktop path
call set "DESKTOP_PATH=%DESKTOP_PATH%"

echo 📁 مسار سطح المكتب: %DESKTOP_PATH%
echo 📂 مسار التطبيق: %CURRENT_DIR%
echo.

REM Check if run-app.bat exists
if not exist "run-app.bat" (
    echo ❌ ملف run-app.bat غير موجود
    echo يرجى التأكد من وجودك في مجلد التطبيق الصحيح
    pause
    exit /b 1
)

REM Check if icon exists
if not exist "icons\app-icon.ico" (
    echo ⚠️  ملف الأيقونة غير موجود، سيتم استخدام الأيقونة الافتراضية
    set "ICON_PATH="
) else (
    set "ICON_PATH=%CURRENT_DIR%\icons\app-icon.ico"
    echo ✅ تم العثور على ملف الأيقونة
)

echo.
echo 🔧 إنشاء اختصار سطح المكتب...

REM Create VBS script to create shortcut
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateShortcut.vbs"
echo sLinkFile = "%DESKTOP_PATH%\مؤسسة وقود المستقبل.lnk" >> "%TEMP%\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateShortcut.vbs"
echo oLink.TargetPath = "%CURRENT_DIR%\run-app.bat" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "%CURRENT_DIR%" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Description = "نظام إدارة مؤسسة وقود المستقبل" >> "%TEMP%\CreateShortcut.vbs"
if defined ICON_PATH (
    echo oLink.IconLocation = "%ICON_PATH%" >> "%TEMP%\CreateShortcut.vbs"
)
echo oLink.Save >> "%TEMP%\CreateShortcut.vbs"

REM Execute VBS script
cscript //nologo "%TEMP%\CreateShortcut.vbs"

REM Clean up
del "%TEMP%\CreateShortcut.vbs"

if exist "%DESKTOP_PATH%\مؤسسة وقود المستقبل.lnk" (
    echo ✅ تم إنشاء اختصار سطح المكتب بنجاح!
    echo.
    echo 📍 مكان الاختصار: %DESKTOP_PATH%\مؤسسة وقود المستقبل.lnk
    echo.
    echo يمكنك الآن النقر المزدوج على الأيقونة لتشغيل التطبيق
) else (
    echo ❌ فشل في إنشاء الاختصار
    echo يرجى المحاولة مرة أخرى أو إنشاء الاختصار يدوياً
)

echo.
echo هل تريد إنشاء اختصار في قائمة ابدأ أيضاً؟ (y/n)
set /p start_menu=
if /i "%start_menu%"=="y" (
    echo.
    echo 🔧 إنشاء اختصار قائمة ابدأ...
    
    REM Get start menu path
    set "START_MENU_PATH=%APPDATA%\Microsoft\Windows\Start Menu\Programs"
    
    REM Create start menu shortcut
    echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateStartMenuShortcut.vbs"
    echo sLinkFile = "%START_MENU_PATH%\مؤسسة وقود المستقبل.lnk" >> "%TEMP%\CreateStartMenuShortcut.vbs"
    echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateStartMenuShortcut.vbs"
    echo oLink.TargetPath = "%CURRENT_DIR%\run-app.bat" >> "%TEMP%\CreateStartMenuShortcut.vbs"
    echo oLink.WorkingDirectory = "%CURRENT_DIR%" >> "%TEMP%\CreateStartMenuShortcut.vbs"
    echo oLink.Description = "نظام إدارة مؤسسة وقود المستقبل" >> "%TEMP%\CreateStartMenuShortcut.vbs"
    if defined ICON_PATH (
        echo oLink.IconLocation = "%ICON_PATH%" >> "%TEMP%\CreateStartMenuShortcut.vbs"
    )
    echo oLink.Save >> "%TEMP%\CreateStartMenuShortcut.vbs"
    
    cscript //nologo "%TEMP%\CreateStartMenuShortcut.vbs"
    del "%TEMP%\CreateStartMenuShortcut.vbs"
    
    if exist "%START_MENU_PATH%\مؤسسة وقود المستقبل.lnk" (
        echo ✅ تم إنشاء اختصار قائمة ابدأ بنجاح!
    ) else (
        echo ❌ فشل في إنشاء اختصار قائمة ابدأ
    )
)

echo.
echo ========================================
echo تم الانتهاء من إنشاء الاختصارات!
echo ========================================
pause
