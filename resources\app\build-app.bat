@echo off
chcp 65001 >nul
title بناء تطبيق مؤسسة وقود المستقبل

echo ========================================
echo    بناء تطبيق مؤسسة وقود المستقبل
echo    Future Fuel Corporation
echo    الإصدار 2.2.0
echo ========================================
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo ❌ لم يتم العثور على مجلد node_modules
    echo يجب تثبيت التبعيات أولاً
    echo يرجى تشغيل install-electron.bat
    pause
    exit /b 1
)

echo اختر نوع البناء:
echo.
echo [1] بناء للويندوز 64-bit
echo [2] بناء للويندوز 32-bit  
echo [3] بناء للويندوز (كلا الإصدارين)
echo [4] إنشاء حزمة فقط (بدون مثبت)
echo [5] بناء شامل (جميع الأنواع)
echo.
set /p build_choice=اختر رقم (1-5): 

if "%build_choice%"=="1" (
    echo.
    echo 🔨 بناء للويندوز 64-bit...
    npm run build-win64
) else if "%build_choice%"=="2" (
    echo.
    echo 🔨 بناء للويندوز 32-bit...
    npm run build-win32
) else if "%build_choice%"=="3" (
    echo.
    echo 🔨 بناء للويندوز (كلا الإصدارين)...
    npm run build-win
) else if "%build_choice%"=="4" (
    echo.
    echo 📦 إنشاء حزمة فقط...
    npm run pack
) else if "%build_choice%"=="5" (
    echo.
    echo 🔨 بناء شامل...
    echo.
    echo [1/3] بناء 64-bit...
    npm run build-win64
    echo.
    echo [2/3] بناء 32-bit...
    npm run build-win32
    echo.
    echo [3/3] إنشاء حزمة...
    npm run pack
) else (
    echo ❌ اختيار غير صحيح
    pause
    exit /b 1
)

echo.
if %errorlevel% equ 0 (
    echo ✅ تم البناء بنجاح!
    echo.
    echo 📁 يمكنك العثور على الملفات المبنية في مجلد: build-output
    echo.
    echo هل تريد فتح مجلد الإخراج؟ (y/n)
    set /p open_folder=
    if /i "%open_folder%"=="y" (
        if exist "build-output" (
            explorer "build-output"
        ) else (
            echo ⚠️  مجلد الإخراج غير موجود
        )
    )
) else (
    echo ❌ فشل في البناء
    echo يرجى مراجعة الأخطاء أعلاه
)

echo.
pause
