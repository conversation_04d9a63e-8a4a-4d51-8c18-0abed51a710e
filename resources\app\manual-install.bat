@echo off
title تثبيت يدوي لـ Electron

echo ========================================
echo    تثبيت يدوي لـ Electron
echo ========================================
echo.

echo هذا الملف سيساعدك في التثبيت اليدوي
echo.
echo الخطوات:
echo.
echo 1. أعد تشغيل الكمبيوتر
echo 2. افتح Command Prompt كمدير
echo 3. انتقل لمجلد التطبيق: cd "%cd%"
echo 4. شغل: npm install electron --save-dev --ignore-scripts
echo 5. شغل: npm start
echo.
echo أو جرب تشغيل التطبيق كصفحة ويب:
echo 6. شغل: start index.html
echo.

echo هل تريد تجربة تشغيل التطبيق كصفحة ويب الآن؟ (y/n)
set /p web_choice=

if /i "%web_choice%"=="y" (
    echo تشغيل التطبيق كصفحة ويب...
    start index.html
    echo تم فتح التطبيق في المتصفح!
) else (
    echo يرجى اتباع الخطوات أعلاه لحل مشكلة التثبيت
)

pause
