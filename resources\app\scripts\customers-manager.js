// نظام إدارة الزبائن المحسن
// Enhanced Customer Management System

class CustomersManager {
    constructor() {
        this.dbManager = null;
        this.customers = [];
        this.isLoading = false;
        this.subscription = null;
        this.searchCache = new Map();
        this.lastSync = null;
    }

    /**
     * تهيئة نظام إدارة الزبائن
     */
    async initialize() {
        try {
            console.log('🚀 تهيئة نظام إدارة الزبائن...');
            
            // تحميل مدير قاعدة البيانات
            if (typeof require !== 'undefined') {
                this.dbManager = require('./database.js');
            } else {
                this.dbManager = window.dbManager;
            }

            if (!this.dbManager) {
                throw new Error('مدير قاعدة البيانات غير متوفر');
            }

            // تهيئة قاعدة البيانات
            await this.dbManager.initialize();

            // تحميل الزبائن
            await this.loadCustomers();

            // الاشتراك في التحديثات المباشرة
            this.setupRealTimeUpdates();

            // إعداد الحفظ التلقائي
            this.setupAutoSync();

            console.log('✅ تم تهيئة نظام إدارة الزبائن بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام إدارة الزبائن:', error);
            
            // التبديل إلى الوضع المحلي
            await this.fallbackToLocalMode();
            return false;
        }
    }

    /**
     * تحميل جميع الزبائن
     */
    async loadCustomers() {
        try {
            this.isLoading = true;
            
            if (this.dbManager && this.dbManager.isOnline()) {
                // تحميل من قاعدة البيانات السحابية
                const result = await this.dbManager.getAllCustomers();
                if (result.success) {
                    this.customers = result.data;
                    this.lastSync = new Date();
                    console.log(`✅ تم تحميل ${this.customers.length} زبون من قاعدة البيانات السحابية`);
                } else {
                    throw new Error(result.error);
                }
            } else {
                // تحميل من التخزين المحلي
                await this.loadFromLocalStorage();
            }

            // تحديث واجهة المستخدم
            this.updateCustomersTable();
            
        } catch (error) {
            console.error('❌ خطأ في تحميل الزبائن:', error);
            await this.loadFromLocalStorage();
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * إضافة زبون جديد
     */
    async addCustomer(customerData) {
        try {
            // التحقق من صحة البيانات
            const validation = this.validateCustomerData(customerData);
            if (!validation.isValid) {
                throw new Error(validation.errors.join('\n'));
            }

            // التحقق من عدم وجود زبون بنفس البيانات
            const duplicate = await this.checkDuplicateCustomer(customerData);
            if (duplicate) {
                throw new Error(`يوجد زبون مسجل بنفس ${duplicate.field}: ${duplicate.value}`);
            }

            let result;
            if (this.dbManager && this.dbManager.isOnline()) {
                // إضافة إلى قاعدة البيانات السحابية
                result = await this.dbManager.addCustomer(customerData);
                if (!result.success) {
                    throw new Error(result.error);
                }
            } else {
                // إضافة محلياً
                result = await this.addCustomerLocally(customerData);
            }

            // تحديث القائمة المحلية
            if (result.success) {
                // إضافة الزبون فقط إذا لم يكن موجوداً مسبقاً (للوضع السحابي)
                if (this.dbManager && this.dbManager.isOnline()) {
                    this.customers.push(result.data);
                }

                this.updateCustomersTable();
                this.clearSearchCache();

                // إظهار رسالة نجاح
                this.showToast('تم إضافة الزبون بنجاح', 'success');

                console.log('✅ تم إضافة الزبون بنجاح:', result.data);
                return result.data;
            }

        } catch (error) {
            console.error('❌ خطأ في إضافة الزبون:', error);
            this.showToast(`خطأ في إضافة الزبون: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * تحديث بيانات زبون
     */
    async updateCustomer(customerId, customerData) {
        try {
            // التحقق من صحة البيانات
            const validation = this.validateCustomerData(customerData, true);
            if (!validation.isValid) {
                throw new Error(validation.errors.join('\n'));
            }

            let result;
            if (this.dbManager && this.dbManager.isOnline()) {
                // تحديث في قاعدة البيانات السحابية
                result = await this.dbManager.updateCustomer(customerId, customerData);
                if (!result.success) {
                    throw new Error(result.error);
                }
            } else {
                // تحديث محلياً
                result = await this.updateCustomerLocally(customerId, customerData);
            }

            // تحديث القائمة المحلية
            if (result.success) {
                const index = this.customers.findIndex(c => c.id === customerId);
                if (index !== -1) {
                    this.customers[index] = result.data;
                }
                this.updateCustomersTable();
                this.clearSearchCache();
                
                this.showToast('تم تحديث بيانات الزبون بنجاح', 'success');
                console.log('✅ تم تحديث الزبون بنجاح:', result.data);
                return result.data;
            }

        } catch (error) {
            console.error('❌ خطأ في تحديث الزبون:', error);
            this.showToast(`خطأ في تحديث الزبون: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * حذف زبون
     */
    async deleteCustomer(customerId) {
        try {
            // التحقق من وجود بيانات مرتبطة
            const hasRelatedData = await this.checkRelatedData(customerId);
            if (hasRelatedData.length > 0) {
                const relatedItems = hasRelatedData.join(', ');
                const confirmDelete = confirm(
                    `تحذير: يوجد بيانات مرتبطة بهذا الزبون (${relatedItems}). ` +
                    'سيتم حذف جميع البيانات المرتبطة. هل تريد المتابعة؟'
                );
                if (!confirmDelete) {
                    return false;
                }
            }

            let result;
            if (this.dbManager && this.dbManager.isOnline()) {
                // حذف من قاعدة البيانات السحابية
                result = await this.dbManager.deleteCustomer(customerId);
                if (!result.success) {
                    throw new Error(result.error);
                }
            } else {
                // حذف محلياً
                result = await this.deleteCustomerLocally(customerId);
            }

            // تحديث القائمة المحلية
            if (result.success) {
                this.customers = this.customers.filter(c => c.id !== customerId);
                this.updateCustomersTable();
                this.clearSearchCache();
                
                this.showToast('تم حذف الزبون بنجاح', 'success');
                console.log('✅ تم حذف الزبون بنجاح');
                return true;
            }

        } catch (error) {
            console.error('❌ خطأ في حذف الزبون:', error);
            this.showToast(`خطأ في حذف الزبون: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * البحث عن الزبائن
     */
    async searchCustomers(searchTerm, filters = {}) {
        try {
            // التحقق من الكاش
            const cacheKey = `${searchTerm}_${JSON.stringify(filters)}`;
            if (this.searchCache.has(cacheKey)) {
                return this.searchCache.get(cacheKey);
            }

            let results;
            if (this.dbManager && this.dbManager.isOnline()) {
                // البحث في قاعدة البيانات السحابية
                const result = await this.dbManager.searchCustomers(searchTerm, filters);
                if (result.success) {
                    results = result.data;
                } else {
                    throw new Error(result.error);
                }
            } else {
                // البحث محلياً
                results = this.searchCustomersLocally(searchTerm, filters);
            }

            // حفظ في الكاش
            this.searchCache.set(cacheKey, results);
            
            // تنظيف الكاش القديم
            if (this.searchCache.size > 50) {
                const firstKey = this.searchCache.keys().next().value;
                this.searchCache.delete(firstKey);
            }

            return results;

        } catch (error) {
            console.error('❌ خطأ في البحث عن الزبائن:', error);
            return [];
        }
    }

    /**
     * التحقق من صحة بيانات الزبون
     */
    validateCustomerData(data, isUpdate = false) {
        const errors = [];

        // التحقق من الاسم
        if (!data.name || data.name.trim().length < 2) {
            errors.push('اسم الزبون مطلوب ويجب أن يكون أكثر من حرفين');
        }

        if (data.name && data.name.length > 255) {
            errors.push('اسم الزبون طويل جداً (الحد الأقصى 255 حرف)');
        }

        // التحقق من رقم الهاتف
        if (!data.phone || !/^[0-9+\-\s()]{8,20}$/.test(data.phone.trim())) {
            errors.push('رقم الهاتف غير صحيح (يجب أن يكون بين 8-20 رقم)');
        }

        // التحقق من البريد الإلكتروني (اختياري)
        if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email.trim())) {
            errors.push('البريد الإلكتروني غير صحيح');
        }

        // التحقق من العنوان
        if (data.address && data.address.length > 1000) {
            errors.push('العنوان طويل جداً (الحد الأقصى 1000 حرف)');
        }

        // التحقق من الملاحظات
        if (data.notes && data.notes.length > 2000) {
            errors.push('الملاحظات طويلة جداً (الحد الأقصى 2000 حرف)');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * التحقق من وجود زبون مكرر
     */
    async checkDuplicateCustomer(customerData) {
        const existingByPhone = this.customers.find(c => c.phone === customerData.phone);
        if (existingByPhone) {
            return { field: 'رقم الهاتف', value: customerData.phone };
        }

        if (customerData.email) {
            const existingByEmail = this.customers.find(c => c.email === customerData.email);
            if (existingByEmail) {
                return { field: 'البريد الإلكتروني', value: customerData.email };
            }
        }

        return null;
    }

    /**
     * إعداد التحديثات المباشرة
     */
    setupRealTimeUpdates() {
        if (this.dbManager && this.dbManager.isOnline()) {
            this.subscription = this.dbManager.subscribeToCustomers((payload) => {
                console.log('🔄 تحديث مباشر للزبائن:', payload);
                this.handleRealTimeUpdate(payload);
            });
        }
    }

    /**
     * معالجة التحديثات المباشرة
     */
    handleRealTimeUpdate(payload) {
        const { eventType, new: newRecord, old: oldRecord } = payload;

        switch (eventType) {
            case 'INSERT':
                this.customers.push(newRecord);
                break;
            case 'UPDATE':
                const updateIndex = this.customers.findIndex(c => c.id === newRecord.id);
                if (updateIndex !== -1) {
                    this.customers[updateIndex] = newRecord;
                }
                break;
            case 'DELETE':
                this.customers = this.customers.filter(c => c.id !== oldRecord.id);
                break;
        }

        // تحديث واجهة المستخدم
        this.updateCustomersTable();
        this.clearSearchCache();
    }

    /**
     * تحديث جدول الزبائن في واجهة المستخدم
     */
    updateCustomersTable() {
        // سيتم تنفيذ هذه الوظيفة لاحقاً مع واجهة المستخدم
        if (typeof updateCustomersTable === 'function') {
            updateCustomersTable();
        }
    }

    /**
     * إظهار رسالة للمستخدم
     */
    showToast(message, type = 'info') {
        if (typeof showToast === 'function') {
            showToast(message, type === 'success');
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    /**
     * مسح كاش البحث
     */
    clearSearchCache() {
        this.searchCache.clear();
    }

    /**
     * التبديل إلى الوضع المحلي
     */
    async fallbackToLocalMode() {
        console.log('🔄 التبديل إلى الوضع المحلي...');
        await this.loadFromLocalStorage();
    }

    /**
     * تحميل البيانات من التخزين المحلي
     */
    async loadFromLocalStorage() {
        try {
            const savedData = localStorage.getItem('gasShopData');
            if (savedData) {
                const data = JSON.parse(savedData);
                this.customers = data.customers || [];
                console.log(`✅ تم تحميل ${this.customers.length} زبون من التخزين المحلي`);
            } else {
                this.customers = [];
                console.log('ℹ️ لا توجد بيانات محفوظة محلياً');
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات المحلية:', error);
            this.customers = [];
        }
    }

    /**
     * إعداد المزامنة التلقائية
     */
    setupAutoSync() {
        // مزامنة كل 5 دقائق
        setInterval(async () => {
            if (this.dbManager && this.dbManager.isOnline()) {
                await this.syncWithCloud();
            }
        }, 5 * 60 * 1000);
    }

    /**
     * مزامنة مع السحابة
     */
    async syncWithCloud() {
        try {
            console.log('🔄 بدء المزامنة مع السحابة...');
            await this.loadCustomers();
            console.log('✅ تمت المزامنة بنجاح');
        } catch (error) {
            console.error('❌ خطأ في المزامنة:', error);
        }
    }

    /**
     * إضافة زبون محلياً (للوضع المحلي)
     */
    async addCustomerLocally(customerData) {
        try {
            const newCustomer = {
                id: this.generateId(),
                name: customerData.name,
                phone: customerData.phone,
                email: customerData.email || null,
                address: customerData.address || null,
                notes: customerData.notes || null,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                is_active: true
            };

            // إضافة الزبون إلى المصفوفة المحلية أولاً
            this.customers.push(newCustomer);

            // حفظ في التخزين المحلي
            await this.saveToLocalStorage();

            return { success: true, data: newCustomer };
        } catch (error) {
            // إزالة الزبون من المصفوفة في حالة فشل الحفظ
            const index = this.customers.findIndex(c => c.id === newCustomer.id);
            if (index !== -1) {
                this.customers.splice(index, 1);
            }
            return { success: false, error: error.message };
        }
    }

    /**
     * تحديث زبون محلياً
     */
    async updateCustomerLocally(customerId, customerData) {
        try {
            const index = this.customers.findIndex(c => c.id === customerId);
            if (index === -1) {
                throw new Error('الزبون غير موجود');
            }

            const updatedCustomer = {
                ...this.customers[index],
                name: customerData.name,
                phone: customerData.phone,
                email: customerData.email || null,
                address: customerData.address || null,
                notes: customerData.notes || null,
                updated_at: new Date().toISOString()
            };

            this.customers[index] = updatedCustomer;
            await this.saveToLocalStorage();

            return { success: true, data: updatedCustomer };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * حذف زبون محلياً
     */
    async deleteCustomerLocally(customerId) {
        try {
            const index = this.customers.findIndex(c => c.id === customerId);
            if (index === -1) {
                throw new Error('الزبون غير موجود');
            }

            this.customers.splice(index, 1);
            await this.saveToLocalStorage();

            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * البحث محلياً
     */
    searchCustomersLocally(searchTerm, filters = {}) {
        let results = [...this.customers];

        // البحث النصي
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            results = results.filter(customer =>
                customer.name.toLowerCase().includes(term) ||
                customer.phone.includes(term) ||
                (customer.email && customer.email.toLowerCase().includes(term))
            );
        }

        // تطبيق الفلاتر
        if (filters.isActive !== undefined) {
            results = results.filter(customer => customer.is_active === filters.isActive);
        }

        // الترتيب
        results.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

        return results;
    }

    /**
     * التحقق من البيانات المرتبطة
     */
    async checkRelatedData(customerId) {
        const relatedData = [];

        // التحقق من السيارات (إذا كانت متوفرة)
        if (typeof appData !== 'undefined' && appData.vehicles) {
            const vehicles = appData.vehicles.filter(v => v.customerId === customerId);
            if (vehicles.length > 0) {
                relatedData.push(`${vehicles.length} سيارة`);
            }
        }

        // التحقق من بطاقات الغاز
        if (typeof appData !== 'undefined' && appData.gasCards) {
            const gasCards = appData.gasCards.filter(gc => gc.customerId === customerId);
            if (gasCards.length > 0) {
                relatedData.push(`${gasCards.length} بطاقة غاز`);
            }
        }

        // التحقق من المواعيد
        if (typeof appData !== 'undefined' && appData.appointments) {
            const appointments = appData.appointments.filter(a => a.customerId === customerId);
            if (appointments.length > 0) {
                relatedData.push(`${appointments.length} موعد`);
            }
        }

        // التحقق من الديون
        if (typeof appData !== 'undefined' && appData.debts) {
            const debts = appData.debts.filter(d => d.customerId === customerId);
            if (debts.length > 0) {
                relatedData.push(`${debts.length} دين`);
            }
        }

        return relatedData;
    }

    /**
     * حفظ في التخزين المحلي
     */
    async saveToLocalStorage() {
        try {
            if (typeof appData !== 'undefined') {
                appData.customers = this.customers;
                localStorage.setItem('gasShopData', JSON.stringify(appData));
            } else {
                localStorage.setItem('customersData', JSON.stringify(this.customers));
            }
        } catch (error) {
            console.error('❌ خطأ في حفظ البيانات محلياً:', error);
            throw error;
        }
    }

    /**
     * توليد معرف فريد
     */
    generateId() {
        return 'customer_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * الحصول على إحصائيات الزبائن
     */
    getCustomersStats() {
        const total = this.customers.length;
        const active = this.customers.filter(c => c.is_active).length;
        const inactive = total - active;

        // إحصائيات إضافية
        const thisMonth = new Date();
        thisMonth.setDate(1);
        thisMonth.setHours(0, 0, 0, 0);

        const newThisMonth = this.customers.filter(c =>
            new Date(c.created_at) >= thisMonth
        ).length;

        return {
            total,
            active,
            inactive,
            newThisMonth,
            lastSync: this.lastSync
        };
    }

    /**
     * تصدير بيانات الزبائن
     */
    exportCustomers(format = 'json') {
        try {
            const data = {
                customers: this.customers,
                exportDate: new Date().toISOString(),
                totalCount: this.customers.length,
                version: '2.2.0'
            };

            if (format === 'json') {
                const dataStr = JSON.stringify(data, null, 2);
                const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

                const exportFileDefaultName = `customers_export_${new Date().toISOString().split('T')[0]}.json`;

                const linkElement = document.createElement('a');
                linkElement.setAttribute('href', dataUri);
                linkElement.setAttribute('download', exportFileDefaultName);
                linkElement.click();

                this.showToast('تم تصدير بيانات الزبائن بنجاح', 'success');
            }
        } catch (error) {
            console.error('❌ خطأ في تصدير البيانات:', error);
            this.showToast('خطأ في تصدير البيانات', 'error');
        }
    }

    /**
     * استيراد بيانات الزبائن
     */
    async importCustomers(file) {
        try {
            const text = await file.text();
            const data = JSON.parse(text);

            if (!data.customers || !Array.isArray(data.customers)) {
                throw new Error('ملف غير صحيح - لا يحتوي على بيانات زبائن صالحة');
            }

            // التحقق من صحة البيانات
            const validCustomers = [];
            const errors = [];

            for (let i = 0; i < data.customers.length; i++) {
                const customer = data.customers[i];
                const validation = this.validateCustomerData(customer);

                if (validation.isValid) {
                    // التحقق من عدم وجود تكرار
                    const duplicate = await this.checkDuplicateCustomer(customer);
                    if (!duplicate) {
                        validCustomers.push({
                            ...customer,
                            id: this.generateId(),
                            created_at: new Date().toISOString(),
                            updated_at: new Date().toISOString()
                        });
                    } else {
                        errors.push(`السطر ${i + 1}: ${duplicate.field} مكرر`);
                    }
                } else {
                    errors.push(`السطر ${i + 1}: ${validation.errors.join(', ')}`);
                }
            }

            if (validCustomers.length === 0) {
                throw new Error('لا توجد بيانات صالحة للاستيراد');
            }

            // إضافة الزبائن الصالحين
            for (const customer of validCustomers) {
                await this.addCustomer(customer);
            }

            let message = `تم استيراد ${validCustomers.length} زبون بنجاح`;
            if (errors.length > 0) {
                message += `\nتم تجاهل ${errors.length} سطر بسبب أخطاء`;
                console.warn('أخطاء الاستيراد:', errors);
            }

            this.showToast(message, 'success');

        } catch (error) {
            console.error('❌ خطأ في استيراد البيانات:', error);
            this.showToast(`خطأ في استيراد البيانات: ${error.message}`, 'error');
        }
    }

    /**
     * تنظيف الموارد
     */
    cleanup() {
        if (this.subscription) {
            this.dbManager.unsubscribe(this.subscription);
        }
        this.clearSearchCache();
    }
}

// إنشاء مثيل واحد من مدير الزبائن
const customersManager = new CustomersManager();

// تصدير المدير
if (typeof module !== 'undefined' && module.exports) {
    module.exports = customersManager;
} else {
    window.customersManager = customersManager;
}
