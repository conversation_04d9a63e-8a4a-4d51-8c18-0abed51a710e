# هيكل المشروع المنظم
## Future Fuel Corporation Management System

```
Future-Fuel-Management/
├── 📁 assets/                          # الموارد والملفات الثابتة
│   ├── 📁 icons/                       # الأيقونات
│   │   ├── app-icon.ico               # أيقونة التطبيق
│   │   ├── app-icon.png               # أيقونة PNG
│   │   └── company-logo.svg           # شعار الشركة
│   ├── 📁 images/                      # الصور
│   └── 📁 sounds/                      # الأصوات (للإشعارات)
│
├── 📁 src/                             # الكود المصدري
│   ├── 📁 auth/                        # نظام المصادقة
│   │   ├── login.html                 # واجهة تسجيل الدخول
│   │   ├── login.css                  # أنماط تسجيل الدخول
│   │   ├── login.js                   # منطق تسجيل الدخول
│   │   ├── activation.js              # نظام التفعيل
│   │   └── license.js                 # نظام الترخيص
│   │
│   ├── 📁 components/                  # المكونات القابلة لإعادة الاستخدام
│   │   ├── modal.js                   # النوافذ المنبثقة
│   │   ├── notifications.js           # نظام الإشعارات
│   │   ├── forms.js                   # النماذج
│   │   └── tables.js                  # الجداول
│   │
│   ├── 📁 data/                        # إدارة البيانات
│   │   ├── database.js                # قاعدة البيانات المحلية
│   │   ├── backup.js                  # النسخ الاحتياطية
│   │   ├── sync.js                    # المزامنة
│   │   └── algeria-data.js            # بيانات الولايات والبلديات
│   │
│   ├── 📁 modules/                     # وحدات التطبيق
│   │   ├── dashboard.js               # لوحة التحكم
│   │   ├── customers.js               # إدارة الزبائن
│   │   ├── gas-cards.js               # بطاقات الغاز
│   │   ├── appointments.js            # المواعيد
│   │   ├── inventory.js               # المخزون
│   │   ├── sales.js                   # المبيعات
│   │   ├── purchases.js               # المشتريات
│   │   ├── debts.js                   # الديون
│   │   ├── suppliers.js               # الموردين
│   │   ├── certificates.js            # الشهادات
│   │   └── transmission.js            # جدول الإرسال
│   │
│   ├── 📁 remote/                      # النظام عن بُعد
│   │   ├── admin-panel.html           # لوحة الإدارة
│   │   ├── admin-panel.css            # أنماط لوحة الإدارة
│   │   ├── admin-panel.js             # منطق لوحة الإدارة
│   │   ├── license-server.js          # خادم الترخيص
│   │   └── remote-api.js              # واجهة برمجة التطبيقات
│   │
│   ├── 📁 styles/                      # الأنماط
│   │   ├── main.css                   # الأنماط الرئيسية
│   │   ├── themes.css                 # الثيمات
│   │   ├── components.css             # أنماط المكونات
│   │   ├── responsive.css             # التجاوب
│   │   └── print.css                  # أنماط الطباعة
│   │
│   └── 📁 utils/                       # الأدوات المساعدة
│       ├── helpers.js                 # دوال مساعدة
│       ├── validators.js              # التحقق من البيانات
│       ├── formatters.js              # تنسيق البيانات
│       ├── security.js                # الأمان
│       └── constants.js               # الثوابت
│
├── 📁 config/                          # ملفات التكوين
│   ├── app-config.js                  # إعدادات التطبيق
│   ├── database-config.js             # إعدادات قاعدة البيانات
│   └── server-config.js               # إعدادات الخادم
│
├── 📁 docs/                            # التوثيق
│   ├── README.md                      # دليل المشروع
│   ├── INSTALLATION.md               # دليل التثبيت
│   ├── USER_GUIDE.md                 # دليل المستخدم
│   ├── DEVELOPER_GUIDE.md            # دليل المطور
│   └── API_DOCS.md                   # توثيق واجهة برمجة التطبيقات
│
├── 📁 installer/                       # ملفات التثبيت
│   ├── setup.iss                     # ملف Inno Setup
│   ├── license.txt                   # رخصة الاستخدام
│   └── readme.txt                    # ملاحظات التثبيت
│
├── 📁 tests/                           # الاختبارات
│   ├── unit/                         # اختبارات الوحدة
│   ├── integration/                  # اختبارات التكامل
│   └── e2e/                          # اختبارات شاملة
│
├── 📁 build/                           # ملفات البناء
│   ├── electron/                     # بناء Electron
│   └── web/                          # بناء الويب
│
├── 📁 backups/                         # النسخ الاحتياطية
│   ├── auto/                         # نسخ تلقائية
│   └── manual/                       # نسخ يدوية
│
├── 📁 logs/                            # ملفات السجلات
│   ├── app.log                       # سجل التطبيق
│   ├── error.log                     # سجل الأخطاء
│   └── access.log                    # سجل الوصول
│
├── 📄 main.js                          # الملف الرئيسي لـ Electron
├── 📄 preload.js                       # ملف Preload
├── 📄 index.html                       # الصفحة الرئيسية
├── 📄 package.json                     # إعدادات المشروع
├── 📄 package-lock.json                # قفل التبعيات
├── 📄 .gitignore                       # ملفات Git المتجاهلة
└── 📄 LICENSE                          # رخصة المشروع
```

## 🎯 فوائد التنظيم الجديد:

### 📂 **تنظيم منطقي:**
- فصل واضح بين المكونات المختلفة
- سهولة العثور على الملفات
- تجميع الملفات ذات الصلة

### 🔧 **سهولة الصيانة:**
- كود أكثر تنظيماً وقابلية للقراءة
- تحديثات أسرع وأكثر دقة
- تقليل التعارضات

### 🚀 **قابلية التوسع:**
- إضافة ميزات جديدة بسهولة
- دعم أفضل للفرق المتعددة
- هيكل قابل للنمو

### 🛡️ **الأمان:**
- فصل منطق المصادقة
- حماية أفضل للبيانات الحساسة
- تحكم دقيق في الصلاحيات

---

**المرحلة التالية:** تطبيق هذا الهيكل وإعادة تنظيم الملفات الموجودة
