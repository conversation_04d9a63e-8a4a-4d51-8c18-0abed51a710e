// إصلاح تكامل نظام العمليات مع الكود الموجود
// Operation Integration Fix with Existing Code

(function() {
    'use strict';

    // انتظار تحميل النظام
    function waitForSystem() {
        return new Promise((resolve) => {
            const checkSystem = () => {
                if (window.operationManager && document.getElementById('customer-form')) {
                    resolve();
                } else {
                    setTimeout(checkSystem, 100);
                }
            };
            checkSystem();
        });
    }

    // تطبيق إصلاح التكامل
    async function applyIntegrationFix() {
        await waitForSystem();

        console.log('🔧 تطبيق إصلاح تكامل نظام العمليات...');

        // إصلاح معالجة نموذج الزبون الموجود
        fixExistingCustomerForm();

        // إصلاح تكامل جدول الإرسال
        fixTransmissionTableIntegration();

        // إصلاح تكامل بطاقات الغاز
        fixGasCardsIntegration();

        // إضافة مراقب للعمليات الجديدة
        setupOperationMonitor();

        console.log('✅ تم تطبيق إصلاح تكامل نظام العمليات');
    }

    /**
     * إصلاح معالجة نموذج الزبون الموجود
     */
    function fixExistingCustomerForm() {
        // البحث عن معالج النموذج الموجود
        const customerForm = document.getElementById('customer-form');
        if (!customerForm) return;

        // إضافة معالج إضافي للعمليات
        customerForm.addEventListener('submit', function(e) {
            // السماح للمعالج الأصلي بالعمل أولاً
            setTimeout(async () => {
                try {
                    await handleOperationAfterCustomerSave();
                } catch (error) {
                    console.error('❌ خطأ في معالجة العملية:', error);
                }
            }, 1000); // انتظار ثانية واحدة للتأكد من حفظ الزبون
        });

        console.log('✅ تم إصلاح معالجة نموذج الزبون');
    }

    /**
     * معالجة العملية بعد حفظ الزبون
     */
    async function handleOperationAfterCustomerSave() {
        const operationType = document.getElementById('operation-type')?.value;
        if (!operationType) return;

        const operationDate = document.getElementById('operation-date')?.value || new Date().toISOString().split('T')[0];
        const operationNotes = document.getElementById('operation-notes')?.value || '';

        // جمع البيانات من النموذج
        const customerData = {
            name: document.getElementById('customer-name')?.value,
            phone: document.getElementById('customer-phone')?.value,
            email: document.getElementById('customer-email')?.value,
            address: document.getElementById('customer-address')?.value
        };

        const vehicleData = {
            plateNumber: document.getElementById('plate-number')?.value,
            brand: document.getElementById('vehicle-brand')?.value,
            model: document.getElementById('vehicle-model')?.value,
            year: document.getElementById('vehicle-year')?.value,
            color: document.getElementById('vehicle-color')?.value
        };

        const tankData = {
            type: document.getElementById('tank-type')?.value,
            brand: document.getElementById('tank-brand')?.value,
            serialNumber: document.getElementById('tank-serial-number')?.value,
            capacity: document.getElementById('tank-capacity')?.value
        };

        // معالجة العملية حسب النوع
        switch (operationType) {
            case 'تركيب':
            case 'مراقبة':
                await addOperationToTransmissionTable({
                    operationType,
                    operationDate,
                    operationNotes,
                    customerData,
                    vehicleData,
                    tankData
                });
                break;

            case 'تجديد':
                await addOperationToGasCards({
                    operationType,
                    operationDate,
                    operationNotes,
                    customerData,
                    vehicleData,
                    tankData
                });
                break;
        }
    }

    /**
     * إضافة عملية إلى جدول الإرسال
     */
    async function addOperationToTransmissionTable(data) {
        try {
            console.log(`📋 إضافة عملية ${data.operationType} إلى جدول الإرسال`);

            const transmissionEntry = {
                type: data.operationType,
                tankNumber: data.tankData?.serialNumber || '',
                carType: `${data.vehicleData?.brand || ''} ${data.vehicleData?.model || ''}`.trim(),
                serialNumber: data.tankData?.serialNumber || '',
                registrationNumber: data.vehicleData?.plateNumber || '',
                ownerName: data.customerData?.name || '',
                phoneNumber: data.customerData?.phone || '',
                operationDate: data.operationDate,
                notes: data.operationNotes,
                source: 'customer_form',
                status: 'مكتمل',
                createdAt: new Date().toISOString()
            };

            // إضافة إلى البيانات المحلية
            if (!window.appData) window.appData = {};
            if (!window.appData.transmissionTable) window.appData.transmissionTable = [];

            transmissionEntry.id = 'trans-' + Date.now();
            window.appData.transmissionTable.push(transmissionEntry);

            // حفظ البيانات
            if (typeof saveData === 'function') {
                saveData();
            }

            // تحديث جدول الإرسال إذا كان مفتوحاً
            if (typeof updateTransmissionTable === 'function') {
                updateTransmissionTable();
            }

            // إظهار إشعار نجاح
            showOperationSuccess(`تم إضافة عملية ${data.operationType} إلى جدول الإرسال`);

            console.log('✅ تم إضافة العملية إلى جدول الإرسال بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إضافة العملية إلى جدول الإرسال:', error);
            showOperationError('فشل في إضافة العملية إلى جدول الإرسال');
        }
    }

    /**
     * إضافة عملية إلى بطاقات الغاز
     */
    async function addOperationToGasCards(data) {
        try {
            console.log('💳 إضافة بطاقة غاز جديدة');

            const gasCard = {
                id: 'card-' + Date.now(),
                cardNumber: generateCardNumber(),
                vehicleNumber: data.vehicleData?.plateNumber || '',
                customerName: data.customerData?.name || '',
                tankNumber: data.tankData?.serialNumber || '',
                serialNumber: data.tankData?.serialNumber || '',
                issueDate: data.operationDate,
                expiryDate: calculateExpiryDate(data.operationDate),
                notes: data.operationNotes,
                operationType: 'تجديد بطاقة',
                operationDate: data.operationDate,
                status: 'نشطة',
                createdAt: new Date().toISOString()
            };

            // إضافة إلى البيانات المحلية
            if (!window.appData) window.appData = {};
            if (!window.appData.gasCards) window.appData.gasCards = [];

            window.appData.gasCards.push(gasCard);

            // حفظ البيانات
            if (typeof saveData === 'function') {
                saveData();
            }

            // تحديث جدول بطاقات الغاز إذا كان مفتوحاً
            if (typeof updateGasCardsTable === 'function') {
                updateGasCardsTable();
            }

            // إظهار إشعار نجاح
            showOperationSuccess('تم إضافة بطاقة غاز جديدة بنجاح');

            console.log('✅ تم إضافة بطاقة الغاز بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إضافة بطاقة الغاز:', error);
            showOperationError('فشل في إضافة بطاقة الغاز');
        }
    }

    /**
     * توليد رقم بطاقة غاز
     */
    function generateCardNumber() {
        const prefix = 'GC';
        const year = new Date().getFullYear();
        const month = String(new Date().getMonth() + 1).padStart(2, '0');
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        return `${prefix}-${year}${month}-${random}`;
    }

    /**
     * حساب تاريخ انتهاء البطاقة
     */
    function calculateExpiryDate(issueDate) {
        const issue = new Date(issueDate);
        const expiry = new Date(issue);
        expiry.setFullYear(expiry.getFullYear() + 1);
        return expiry.toISOString().split('T')[0];
    }

    /**
     * إصلاح تكامل جدول الإرسال
     */
    function fixTransmissionTableIntegration() {
        // إضافة وظيفة تحديث جدول الإرسال إذا لم تكن موجودة
        if (typeof window.updateTransmissionTable !== 'function') {
            window.updateTransmissionTable = function() {
                console.log('🔄 تحديث جدول الإرسال...');
                
                // البحث عن جدول الإرسال
                const table = document.querySelector('#transmission-table tbody');
                if (!table) return;

                const transmissionData = window.appData?.transmissionTable || [];
                
                table.innerHTML = '';
                
                transmissionData.forEach(entry => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td><span class="operation-type ${entry.type}">${entry.type}</span></td>
                        <td>${entry.tankNumber || '-'}</td>
                        <td>${entry.carType || '-'}</td>
                        <td>${entry.serialNumber || '-'}</td>
                        <td><strong>${entry.registrationNumber}</strong></td>
                        <td>${entry.ownerName}</td>
                        <td>${entry.phoneNumber || '-'}</td>
                        <td>${formatDate(entry.operationDate)}</td>
                        <td><span class="status ${entry.status}">${entry.status}</span></td>
                        <td>
                            <button onclick="editTransmissionEntry('${entry.id}')" class="btn btn-sm btn-primary">تعديل</button>
                            <button onclick="deleteTransmissionEntry('${entry.id}')" class="btn btn-sm btn-danger">حذف</button>
                        </td>
                    `;
                    table.appendChild(row);
                });
            };
        }

        console.log('✅ تم إصلاح تكامل جدول الإرسال');
    }

    /**
     * إصلاح تكامل بطاقات الغاز
     */
    function fixGasCardsIntegration() {
        // إضافة وظيفة تحديث جدول بطاقات الغاز إذا لم تكن موجودة
        if (typeof window.updateGasCardsTable !== 'function') {
            window.updateGasCardsTable = function() {
                console.log('🔄 تحديث جدول بطاقات الغاز...');
                
                // البحث عن جدول بطاقات الغاز
                const table = document.querySelector('#gas-cards-table tbody');
                if (!table) return;

                const gasCardsData = window.appData?.gasCards || [];
                
                table.innerHTML = '';
                
                gasCardsData.forEach(card => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${card.cardNumber}</td>
                        <td>${card.customerName}</td>
                        <td>${card.vehicleNumber}</td>
                        <td>${card.tankNumber}</td>
                        <td>${formatDate(card.issueDate)}</td>
                        <td>${formatDate(card.expiryDate)}</td>
                        <td><span class="status ${card.status}">${card.status}</span></td>
                        <td>
                            <button onclick="editGasCard('${card.id}')" class="btn btn-sm btn-primary">تعديل</button>
                            <button onclick="deleteGasCard('${card.id}')" class="btn btn-sm btn-danger">حذف</button>
                            <button onclick="printGasCard('${card.id}')" class="btn btn-sm btn-info">طباعة</button>
                        </td>
                    `;
                    table.appendChild(row);
                });
            };
        }

        console.log('✅ تم إصلاح تكامل بطاقات الغاز');
    }

    /**
     * إعداد مراقب العمليات
     */
    function setupOperationMonitor() {
        // مراقبة تغيير نوع العملية
        const operationTypeSelect = document.getElementById('operation-type');
        if (operationTypeSelect) {
            operationTypeSelect.addEventListener('change', function(e) {
                updateOperationLabels(e.target.value);
            });
        }

        console.log('✅ تم إعداد مراقب العمليات');
    }

    /**
     * تحديث تسميات العملية
     */
    function updateOperationLabels(operationType) {
        const operationDateLabel = document.getElementById('operation-date-label');
        const operationNotesLabel = document.querySelector('label[for="operation-notes"]');

        if (operationDateLabel) {
            switch (operationType) {
                case 'تركيب':
                    operationDateLabel.textContent = 'تاريخ التركيب:';
                    break;
                case 'مراقبة':
                    operationDateLabel.textContent = 'تاريخ المراقبة:';
                    break;
                case 'تجديد':
                    operationDateLabel.textContent = 'تاريخ التجديد:';
                    break;
                default:
                    operationDateLabel.textContent = 'تاريخ العملية:';
            }
        }

        if (operationNotesLabel) {
            switch (operationType) {
                case 'تركيب':
                    operationNotesLabel.textContent = 'ملاحظات التركيب:';
                    break;
                case 'مراقبة':
                    operationNotesLabel.textContent = 'ملاحظات المراقبة:';
                    break;
                case 'تجديد':
                    operationNotesLabel.textContent = 'ملاحظات التجديد:';
                    break;
                default:
                    operationNotesLabel.textContent = 'ملاحظات:';
            }
        }
    }

    /**
     * تنسيق التاريخ
     */
    function formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    /**
     * عرض رسالة نجاح العملية
     */
    function showOperationSuccess(message) {
        if (window.showEnhancedToast) {
            window.showEnhancedToast(message, 'success', 4000);
        } else if (window.showToast) {
            window.showToast(message, true);
        } else {
            console.log('✅ ' + message);
        }
    }

    /**
     * عرض رسالة خطأ العملية
     */
    function showOperationError(message) {
        if (window.showEnhancedToast) {
            window.showEnhancedToast(message, 'error', 4000);
        } else if (window.showToast) {
            window.showToast(message, false);
        } else {
            console.error('❌ ' + message);
        }
    }

    // تطبيق الإصلاح عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(applyIntegrationFix, 2500);
        });
    } else {
        setTimeout(applyIntegrationFix, 2500);
    }

    // تصدير الوظائف للاستخدام العام
    window.operationIntegrationFix = {
        applyIntegrationFix,
        addOperationToTransmissionTable,
        addOperationToGasCards
    };

})();
