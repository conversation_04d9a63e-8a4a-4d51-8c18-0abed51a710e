// إصلاح نموذج إضافة الزبائن
// Customer Form Fix for Enhanced System

class CustomerFormFix {
    constructor() {
        this.isInitialized = false;
    }

    /**
     * تهيئة النموذج المحسن
     */
    initialize() {
        if (this.isInitialized) return;

        console.log('🔧 تهيئة نموذج إضافة الزبائن المحسن...');

        // إنشاء نموذج مبسط لإضافة الزبائن
        this.createSimpleCustomerForm();
        
        // إعداد أحداث النموذج
        this.setupFormEvents();
        
        // استبدال الوظائف القديمة
        this.replaceOldFunctions();

        this.isInitialized = true;
        console.log('✅ تم تهيئة نموذج إضافة الزبائن المحسن');
    }

    /**
     * إنشاء نموذج مبسط لإضافة الزبائن
     */
    createSimpleCustomerForm() {
        // البحث عن النموذج الموجود
        let modal = document.getElementById('simple-customer-modal');
        
        if (!modal) {
            // إنشاء نموذج جديد مبسط
            modal = document.createElement('div');
            modal.id = 'simple-customer-modal';
            modal.className = 'modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>إضافة زبون جديد</h2>
                        <span class="close" id="close-simple-customer">&times;</span>
                    </div>
                    <form id="simple-customer-form">
                        <div class="form-group">
                            <label for="simple-customer-name">اسم الزبون <span class="required">*</span></label>
                            <input type="text" id="simple-customer-name" required placeholder="أدخل اسم الزبون">
                            <div class="error-message"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="simple-customer-phone">رقم الهاتف <span class="required">*</span></label>
                            <input type="tel" id="simple-customer-phone" required placeholder="أدخل رقم الهاتف">
                            <div class="error-message"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="simple-customer-email">البريد الإلكتروني</label>
                            <input type="email" id="simple-customer-email" placeholder="أدخل البريد الإلكتروني (اختياري)">
                            <div class="error-message"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="simple-customer-address">العنوان</label>
                            <textarea id="simple-customer-address" rows="3" placeholder="أدخل العنوان (اختياري)"></textarea>
                            <div class="error-message"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="simple-customer-notes">ملاحظات</label>
                            <textarea id="simple-customer-notes" rows="2" placeholder="أدخل ملاحظات إضافية (اختياري)"></textarea>
                            <div class="error-message"></div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" class="btn secondary" id="cancel-simple-customer">إلغاء</button>
                            <button type="submit" class="btn primary" id="save-simple-customer">حفظ الزبون</button>
                        </div>
                    </form>
                </div>
            `;
            
            document.body.appendChild(modal);
        }

        // إضافة أنماط CSS للنموذج
        this.addFormStyles();
    }

    /**
     * إضافة أنماط CSS للنموذج
     */
    addFormStyles() {
        if (document.getElementById('simple-customer-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'simple-customer-styles';
        styles.textContent = `
            #simple-customer-modal .modal-content {
                max-width: 500px;
                margin: 5% auto;
            }
            
            #simple-customer-modal .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 1px solid #eee;
            }
            
            #simple-customer-modal .form-group {
                margin-bottom: 15px;
            }
            
            #simple-customer-modal label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
                color: #333;
            }
            
            #simple-customer-modal .required {
                color: #dc3545;
            }
            
            #simple-customer-modal input,
            #simple-customer-modal textarea {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                transition: border-color 0.3s ease;
            }
            
            #simple-customer-modal input:focus,
            #simple-customer-modal textarea:focus {
                outline: none;
                border-color: #007bff;
                box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
            }
            
            #simple-customer-modal .error-message {
                color: #dc3545;
                font-size: 12px;
                margin-top: 5px;
                display: none;
            }
            
            #simple-customer-modal .form-actions {
                display: flex;
                justify-content: flex-end;
                gap: 10px;
                margin-top: 20px;
                padding-top: 15px;
                border-top: 1px solid #eee;
            }
            
            #simple-customer-modal .btn {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                transition: background-color 0.3s ease;
            }
            
            #simple-customer-modal .btn.primary {
                background-color: #007bff;
                color: white;
            }
            
            #simple-customer-modal .btn.primary:hover {
                background-color: #0056b3;
            }
            
            #simple-customer-modal .btn.secondary {
                background-color: #6c757d;
                color: white;
            }
            
            #simple-customer-modal .btn.secondary:hover {
                background-color: #545b62;
            }
            
            #simple-customer-modal .loading {
                opacity: 0.7;
                pointer-events: none;
            }
        `;
        
        document.head.appendChild(styles);
    }

    /**
     * إعداد أحداث النموذج
     */
    setupFormEvents() {
        const modal = document.getElementById('simple-customer-modal');
        const form = document.getElementById('simple-customer-form');
        const closeBtn = document.getElementById('close-simple-customer');
        const cancelBtn = document.getElementById('cancel-simple-customer');

        // إغلاق النموذج
        const closeModal = () => {
            modal.style.display = 'none';
            this.resetForm();
        };

        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);

        // إغلاق عند النقر خارج النموذج
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal();
            }
        });

        // معالجة إرسال النموذج
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFormSubmit();
        });

        // التحقق من البيانات أثناء الكتابة
        const inputs = form.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
        });
    }

    /**
     * فتح النموذج
     */
    openForm() {
        const modal = document.getElementById('simple-customer-modal');
        modal.style.display = 'block';
        
        // التركيز على أول حقل
        const firstInput = document.getElementById('simple-customer-name');
        setTimeout(() => firstInput.focus(), 100);
    }

    /**
     * إعادة تعيين النموذج
     */
    resetForm() {
        const form = document.getElementById('simple-customer-form');
        form.reset();
        
        // إزالة رسائل الخطأ
        const errorMessages = form.querySelectorAll('.error-message');
        errorMessages.forEach(msg => {
            msg.style.display = 'none';
            msg.textContent = '';
        });
        
        // إزالة كلاسات الخطأ
        const inputs = form.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.classList.remove('error');
        });
    }

    /**
     * التحقق من حقل واحد
     */
    validateField(input) {
        const value = input.value.trim();
        const errorElement = input.parentElement.querySelector('.error-message');
        let isValid = true;
        let errorMessage = '';

        switch (input.id) {
            case 'simple-customer-name':
                if (!value) {
                    isValid = false;
                    errorMessage = 'اسم الزبون مطلوب';
                } else if (value.length < 2) {
                    isValid = false;
                    errorMessage = 'اسم الزبون يجب أن يكون أكثر من حرفين';
                }
                break;

            case 'simple-customer-phone':
                if (!value) {
                    isValid = false;
                    errorMessage = 'رقم الهاتف مطلوب';
                } else if (!/^[0-9+\-\s()]{8,20}$/.test(value)) {
                    isValid = false;
                    errorMessage = 'رقم الهاتف غير صحيح';
                }
                break;

            case 'simple-customer-email':
                if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                    isValid = false;
                    errorMessage = 'البريد الإلكتروني غير صحيح';
                }
                break;
        }

        // عرض أو إخفاء رسالة الخطأ
        if (isValid) {
            input.classList.remove('error');
            errorElement.style.display = 'none';
        } else {
            input.classList.add('error');
            errorElement.textContent = errorMessage;
            errorElement.style.display = 'block';
        }

        return isValid;
    }

    /**
     * التحقق من جميع الحقول
     */
    validateForm() {
        const form = document.getElementById('simple-customer-form');
        const inputs = form.querySelectorAll('input[required], textarea[required]');
        let isValid = true;

        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });

        return isValid;
    }

    /**
     * معالجة إرسال النموذج
     */
    async handleFormSubmit() {
        try {
            // التحقق من صحة البيانات
            if (!this.validateForm()) {
                return;
            }

            // إظهار حالة التحميل
            const submitBtn = document.getElementById('save-simple-customer');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'جاري الحفظ...';
            submitBtn.disabled = true;

            // جمع البيانات
            const customerData = {
                name: document.getElementById('simple-customer-name').value.trim(),
                phone: document.getElementById('simple-customer-phone').value.trim(),
                email: document.getElementById('simple-customer-email').value.trim() || null,
                address: document.getElementById('simple-customer-address').value.trim() || null,
                notes: document.getElementById('simple-customer-notes').value.trim() || null
            };

            // إضافة الزبون
            if (window.customersManager) {
                await window.customersManager.addCustomer(customerData);
                
                // إغلاق النموذج
                document.getElementById('simple-customer-modal').style.display = 'none';
                this.resetForm();
                
            } else {
                throw new Error('نظام إدارة الزبائن غير متوفر');
            }

        } catch (error) {
            console.error('❌ خطأ في إضافة الزبون:', error);
            
            if (window.showEnhancedToast) {
                window.showEnhancedToast(`خطأ في إضافة الزبون: ${error.message}`, 'error');
            } else {
                alert(`خطأ في إضافة الزبون: ${error.message}`);
            }
        } finally {
            // إعادة تعيين زر الحفظ
            const submitBtn = document.getElementById('save-simple-customer');
            submitBtn.textContent = 'حفظ الزبون';
            submitBtn.disabled = false;
        }
    }

    /**
     * استبدال الوظائف القديمة
     */
    replaceOldFunctions() {
        // استبدال زر إضافة الزبون القديم
        const oldAddBtn = document.getElementById('add-customer-btn');
        if (oldAddBtn) {
            // إنشاء زر جديد
            const newAddBtn = document.createElement('button');
            newAddBtn.id = 'add-customer-btn-new';
            newAddBtn.className = oldAddBtn.className;
            newAddBtn.innerHTML = '<i class="fas fa-plus"></i> إضافة زبون جديد';
            
            // استبدال الزر القديم
            oldAddBtn.parentNode.insertBefore(newAddBtn, oldAddBtn);
            oldAddBtn.style.display = 'none';
            
            // إضافة حدث للزر الجديد
            newAddBtn.addEventListener('click', () => {
                this.openForm();
            });
        }

        // إضافة زر إضافة سريع إذا لم يكن موجوداً
        if (!document.getElementById('quick-add-customer-btn')) {
            const quickAddBtn = document.createElement('button');
            quickAddBtn.id = 'quick-add-customer-btn';
            quickAddBtn.className = 'btn btn-success btn-sm';
            quickAddBtn.innerHTML = '<i class="fas fa-user-plus"></i> إضافة سريع';
            quickAddBtn.style.cssText = 'margin-left: 10px;';
            
            quickAddBtn.addEventListener('click', () => {
                this.openForm();
            });

            // إضافة الزر بجانب زر البحث
            const searchContainer = document.querySelector('.search-container') || 
                                  document.querySelector('.customers-header') ||
                                  document.querySelector('h2');
            
            if (searchContainer) {
                searchContainer.appendChild(quickAddBtn);
            }
        }
    }
}

// إنشاء مثيل من إصلاح النموذج
const customerFormFix = new CustomerFormFix();

// تصدير الكلاس
if (typeof module !== 'undefined' && module.exports) {
    module.exports = customerFormFix;
} else {
    window.customerFormFix = customerFormFix;
}

// تهيئة تلقائية عند تحميل الصفحة
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            customerFormFix.initialize();
        }, 1000);
    });
}
