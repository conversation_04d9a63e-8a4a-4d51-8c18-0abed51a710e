// إصلاح مشكلة إضافة الزبائن
// Customer Addition Fix

(function() {
    'use strict';

    // التأكد من تحميل النظام
    function ensureSystemReady() {
        return new Promise((resolve) => {
            const checkSystem = () => {
                if (window.customersManager && window.systemIntegration) {
                    resolve();
                } else {
                    setTimeout(checkSystem, 100);
                }
            };
            checkSystem();
        });
    }

    // إصلاح مشكلة إضافة الزبائن
    async function fixCustomerAddition() {
        await ensureSystemReady();

        console.log('🔧 تطبيق إصلاح مشكلة إضافة الزبائن...');

        // التأكد من تحميل البيانات
        if (window.customersManager.customers.length === 0) {
            try {
                await window.customersManager.loadCustomers();
                console.log('✅ تم تحميل بيانات الزبائن');
            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات الزبائن:', error);
            }
        }

        // إصلاح وظيفة حفظ النموذج القديمة
        const originalCustomerForm = document.getElementById('customer-form');
        if (originalCustomerForm) {
            // إزالة المستمعين القدامى
            const newForm = originalCustomerForm.cloneNode(true);
            originalCustomerForm.parentNode.replaceChild(newForm, originalCustomerForm);

            // إضافة مستمع جديد محسن
            newForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                try {
                    // جمع البيانات من النموذج
                    const formData = new FormData(newForm);
                    const customerData = {
                        name: formData.get('customer-name') || document.getElementById('customer-name')?.value,
                        phone: formData.get('customer-phone') || document.getElementById('customer-phone')?.value,
                        email: formData.get('customer-email') || document.getElementById('customer-email')?.value,
                        address: formData.get('customer-address') || document.getElementById('customer-address')?.value,
                        notes: formData.get('customer-notes') || document.getElementById('customer-notes')?.value
                    };

                    // تنظيف البيانات
                    Object.keys(customerData).forEach(key => {
                        if (customerData[key]) {
                            customerData[key] = customerData[key].toString().trim();
                        }
                    });

                    // التحقق من البيانات الأساسية
                    if (!customerData.name || !customerData.phone) {
                        throw new Error('الاسم ورقم الهاتف مطلوبان');
                    }

                    // إضافة الزبون باستخدام النظام المحسن
                    if (window.customersManager) {
                        await window.customersManager.addCustomer(customerData);
                        
                        // إغلاق النموذج
                        const modal = document.getElementById('customer-modal');
                        if (modal) {
                            modal.style.display = 'none';
                        }
                        
                        // إعادة تعيين النموذج
                        newForm.reset();
                        
                    } else {
                        throw new Error('نظام إدارة الزبائن غير متوفر');
                    }

                } catch (error) {
                    console.error('❌ خطأ في إضافة الزبون:', error);
                    
                    // عرض رسالة خطأ للمستخدم
                    if (window.showToast) {
                        window.showToast(`خطأ: ${error.message}`, false);
                    } else if (window.showEnhancedToast) {
                        window.showEnhancedToast(`خطأ: ${error.message}`, 'error');
                    } else {
                        alert(`خطأ: ${error.message}`);
                    }
                }
            });
        }

        // إصلاح مشكلة تحديث الجدول
        const originalUpdateFunction = window.updateCustomersTable;
        window.updateCustomersTable = function() {
            try {
                // تحديث appData أولاً
                if (window.customersManager && window.appData) {
                    window.appData.customers = window.customersManager.customers;
                }

                // استدعاء الوظيفة الأصلية
                if (originalUpdateFunction && typeof originalUpdateFunction === 'function') {
                    originalUpdateFunction.call(this);
                }

                // تحديث إضافي للجدول
                updateCustomersTableDirect();

            } catch (error) {
                console.error('❌ خطأ في تحديث جدول الزبائن:', error);
            }
        };

        // وظيفة تحديث مباشرة للجدول
        function updateCustomersTableDirect() {
            const table = document.getElementById('customers-table');
            if (!table) return;

            const tbody = table.querySelector('tbody');
            if (!tbody) return;

            const customers = window.customersManager ? window.customersManager.customers : (window.appData?.customers || []);
            
            tbody.innerHTML = '';

            customers.forEach(customer => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${customer.name || ''}</td>
                    <td>${customer.phone || ''}</td>
                    <td>${customer.email || ''}</td>
                    <td>${customer.address || ''}</td>
                    <td>
                        <button onclick="editCustomer('${customer.id}')" class="btn btn-sm btn-primary">تعديل</button>
                        <button onclick="deleteCustomer('${customer.id}')" class="btn btn-sm btn-danger">حذف</button>
                        <button onclick="showCustomerInfo('${customer.id}')" class="btn btn-sm btn-info">عرض</button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // تحديث العداد
            const countElement = document.querySelector('.customers-count, #customers-count');
            if (countElement) {
                countElement.textContent = customers.length;
            }
        }

        // إصلاح مشكلة البحث
        const searchInput = document.querySelector('input[placeholder*="بحث"]');
        if (searchInput) {
            // إزالة المستمعين القدامى
            const newSearchInput = searchInput.cloneNode(true);
            searchInput.parentNode.replaceChild(newSearchInput, searchInput);

            // إضافة مستمع جديد للبحث
            let searchTimeout;
            newSearchInput.addEventListener('input', function(e) {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(async () => {
                    const searchTerm = e.target.value.trim();
                    
                    try {
                        if (window.customersManager) {
                            const results = await window.customersManager.searchCustomers(searchTerm);
                            
                            // تحديث الجدول بنتائج البحث
                            const table = document.getElementById('customers-table');
                            if (table) {
                                const tbody = table.querySelector('tbody');
                                if (tbody) {
                                    tbody.innerHTML = '';
                                    
                                    results.forEach(customer => {
                                        const row = document.createElement('tr');
                                        row.innerHTML = `
                                            <td>${customer.name || ''}</td>
                                            <td>${customer.phone || ''}</td>
                                            <td>${customer.email || ''}</td>
                                            <td>${customer.address || ''}</td>
                                            <td>
                                                <button onclick="editCustomer('${customer.id}')" class="btn btn-sm btn-primary">تعديل</button>
                                                <button onclick="deleteCustomer('${customer.id}')" class="btn btn-sm btn-danger">حذف</button>
                                                <button onclick="showCustomerInfo('${customer.id}')" class="btn btn-sm btn-info">عرض</button>
                                            </td>
                                        `;
                                        tbody.appendChild(row);
                                    });
                                }
                            }
                        }
                    } catch (error) {
                        console.error('❌ خطأ في البحث:', error);
                    }
                }, 300);
            });
        }

        // إضافة مراقب للتغييرات في appData
        if (window.appData && window.customersManager) {
            // مزامنة البيانات عند التغيير
            const originalCustomers = window.appData.customers;
            Object.defineProperty(window.appData, 'customers', {
                get: function() {
                    return window.customersManager.customers;
                },
                set: function(value) {
                    if (Array.isArray(value) && window.customersManager) {
                        window.customersManager.customers = value;
                    }
                },
                configurable: true
            });
        }

        console.log('✅ تم تطبيق إصلاح مشكلة إضافة الزبائن بنجاح');
    }

    // تطبيق الإصلاح عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(fixCustomerAddition, 2000);
        });
    } else {
        setTimeout(fixCustomerAddition, 2000);
    }

    // تصدير الوظيفة للاستخدام اليدوي
    window.fixCustomerAddition = fixCustomerAddition;

})();
