@echo off
title Future Fuel Management System
color 0A

echo =============================================
echo    Future Fuel Management System
echo                Version 2.2.0
echo          مع جدول الإرسال المتكامل
echo =============================================
echo.

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Node.js غير مثبت. سيتم فتح التطبيق في المتصفح...
    echo.
    start "" "index.html"
    goto :end
)

REM التحقق من وجود node_modules
if not exist "node_modules" (
    echo تثبيت المتطلبات...
    call npm install
    if %ERRORLEVEL% NEQ 0 (
        echo فشل في تثبيت المتطلبات. سيتم فتح التطبيق في المتصفح...
        start "" "index.html"
        goto :end
    )
)

echo بدء تشغيل التطبيق...
echo.
echo اختر طريقة التشغيل:
echo [1] تطبيق Electron (مستحسن)
echo [2] خادم ويب محلي
echo [3] فتح في المتصفح مباشرة
echo.
set /p choice="أدخل اختيارك (1-3): "

if "%choice%"=="1" (
    echo تشغيل تطبيق Electron...
    call npm run electron
) else if "%choice%"=="2" (
    echo تشغيل الخادم المحلي...
    echo سيتم فتح التطبيق في المتصفح على http://localhost:3000
    call npm run web
) else if "%choice%"=="3" (
    echo فتح في المتصفح...
    start "" "index.html"
) else (
    echo اختيار غير صحيح. سيتم فتح التطبيق في المتصفح...
    start "" "index.html"
)

:end
echo.
echo شكراً لاستخدام نظام إدارة مؤسسة وقود المستقبل!
pause
