# PowerShell script to install/update Electron
Write-Host "Installing/Updating Electron and dependencies..." -ForegroundColor Green
Write-Host ""

# Check if npm is available
try {
    $npmVersion = npm --version
    Write-Host "npm version: $npmVersion" -ForegroundColor Blue
} catch {
    Write-Host "Error: npm is not available or not in PATH" -ForegroundColor Red
    Write-Host "Please make sure Node.js is properly installed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Current directory: $(Get-Location)" -ForegroundColor Blue
Write-Host ""

Write-Host "Installing Electron (latest version)..." -ForegroundColor Yellow
npm install electron --save-dev

Write-Host ""
Write-Host "Installing/Updating other dependencies..." -ForegroundColor Yellow
npm install

Write-Host ""
Write-Host "Installation completed!" -ForegroundColor Green
Write-Host ""
Write-Host "You can now run the application with:" -ForegroundColor Cyan
Write-Host "  npm start" -ForegroundColor White
Write-Host ""
Write-Host "Or build the application with:" -ForegroundColor Cyan
Write-Host "  npm run build" -ForegroundColor White
Write-Host ""
Read-Host "Press Enter to exit"
