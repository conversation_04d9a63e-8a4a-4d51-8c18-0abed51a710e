// إعدادات قاعدة البيانات السحابية - Supabase
// Database Configuration for Future Fuel Management System

const { createClient } = require('@supabase/supabase-js');

// إعدادات Supabase
const SUPABASE_CONFIG = {
    // يجب تحديث هذه القيم من لوحة تحكم Supabase
    url: process.env.SUPABASE_URL || 'https://your-project.supabase.co',
    anonKey: process.env.SUPABASE_ANON_KEY || 'your-anon-key',
    
    // إعدادات الاتصال
    auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false
    },
    
    // إعدادات Real-time
    realtime: {
        params: {
            eventsPerSecond: 10
        }
    }
};

// إنشاء عميل Supabase
let supabase = null;

/**
 * تهيئة اتصال قاعدة البيانات
 */
function initializeDatabase() {
    try {
        if (!supabase) {
            supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.anonKey, {
                auth: SUPABASE_CONFIG.auth,
                realtime: SUPABASE_CONFIG.realtime
            });
            
            console.log('✅ تم تهيئة اتصال قاعدة البيانات بنجاح');
            return true;
        }
        return true;
    } catch (error) {
        console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
        return false;
    }
}

/**
 * الحصول على عميل قاعدة البيانات
 */
function getDatabase() {
    if (!supabase) {
        initializeDatabase();
    }
    return supabase;
}

/**
 * اختبار الاتصال بقاعدة البيانات
 */
async function testConnection() {
    try {
        const db = getDatabase();
        const { data, error } = await db.from('customers').select('count').limit(1);
        
        if (error) {
            console.error('❌ فشل اختبار الاتصال:', error);
            return false;
        }
        
        console.log('✅ اختبار الاتصال نجح');
        return true;
    } catch (error) {
        console.error('❌ خطأ في اختبار الاتصال:', error);
        return false;
    }
}

/**
 * إعداد المخطط الأساسي لقاعدة البيانات
 */
const DATABASE_SCHEMA = {
    // جدول الزبائن
    customers: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        name: 'varchar(255) NOT NULL',
        phone: 'varchar(20) UNIQUE NOT NULL',
        email: 'varchar(255) UNIQUE',
        address: 'text',
        notes: 'text',
        created_at: 'timestamp with time zone DEFAULT now()',
        updated_at: 'timestamp with time zone DEFAULT now()',
        is_active: 'boolean DEFAULT true'
    },
    
    // جدول السيارات
    vehicles: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        customer_id: 'uuid REFERENCES customers(id) ON DELETE CASCADE',
        plate_number: 'varchar(20) UNIQUE NOT NULL',
        brand: 'varchar(100) NOT NULL',
        model: 'varchar(100) NOT NULL',
        year: 'integer',
        color: 'varchar(50)',
        chassis_number: 'varchar(100)',
        engine_number: 'varchar(100)',
        created_at: 'timestamp with time zone DEFAULT now()',
        updated_at: 'timestamp with time zone DEFAULT now()',
        is_active: 'boolean DEFAULT true'
    },
    
    // جدول خزانات الغاز
    gas_tanks: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        vehicle_id: 'uuid REFERENCES vehicles(id) ON DELETE CASCADE',
        tank_type: 'varchar(50) NOT NULL',
        tank_brand: 'varchar(100) NOT NULL',
        serial_number: 'varchar(100) UNIQUE NOT NULL',
        capacity: 'decimal(10,2) NOT NULL',
        manufacturing_date: 'date',
        installation_date: 'date',
        last_inspection_date: 'date',
        next_inspection_date: 'date',
        created_at: 'timestamp with time zone DEFAULT now()',
        updated_at: 'timestamp with time zone DEFAULT now()',
        is_active: 'boolean DEFAULT true'
    },
    
    // جدول بطاقات الغاز
    gas_cards: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        customer_id: 'uuid REFERENCES customers(id) ON DELETE CASCADE',
        vehicle_id: 'uuid REFERENCES vehicles(id) ON DELETE CASCADE',
        card_number: 'varchar(50) UNIQUE NOT NULL',
        issue_date: 'date NOT NULL',
        expiry_date: 'date NOT NULL',
        status: 'varchar(20) DEFAULT \'active\'',
        created_at: 'timestamp with time zone DEFAULT now()',
        updated_at: 'timestamp with time zone DEFAULT now()'
    },
    
    // جدول المواعيد
    appointments: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        customer_id: 'uuid REFERENCES customers(id) ON DELETE CASCADE',
        vehicle_id: 'uuid REFERENCES vehicles(id) ON DELETE CASCADE',
        appointment_date: 'timestamp with time zone NOT NULL',
        service_type: 'varchar(100) NOT NULL',
        status: 'varchar(20) DEFAULT \'scheduled\'',
        notes: 'text',
        created_at: 'timestamp with time zone DEFAULT now()',
        updated_at: 'timestamp with time zone DEFAULT now()'
    },
    
    // جدول الديون
    debts: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        customer_id: 'uuid REFERENCES customers(id) ON DELETE CASCADE',
        amount: 'decimal(10,2) NOT NULL',
        description: 'text',
        due_date: 'date',
        status: 'varchar(20) DEFAULT \'pending\'',
        created_at: 'timestamp with time zone DEFAULT now()',
        updated_at: 'timestamp with time zone DEFAULT now()'
    },
    
    // جدول مدفوعات الديون
    debt_payments: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        debt_id: 'uuid REFERENCES debts(id) ON DELETE CASCADE',
        amount: 'decimal(10,2) NOT NULL',
        payment_date: 'timestamp with time zone DEFAULT now()',
        payment_method: 'varchar(50)',
        notes: 'text',
        created_at: 'timestamp with time zone DEFAULT now()'
    }
};

/**
 * إنشاء الجداول إذا لم تكن موجودة
 */
async function createTables() {
    try {
        const db = getDatabase();
        
        // تفعيل Row Level Security
        await db.rpc('enable_rls_for_all_tables');
        
        console.log('✅ تم إنشاء جميع الجداول بنجاح');
        return true;
    } catch (error) {
        console.error('❌ خطأ في إنشاء الجداول:', error);
        return false;
    }
}

/**
 * إعداد سياسات الأمان (Row Level Security)
 */
async function setupSecurity() {
    try {
        const db = getDatabase();
        
        // سياسات أمان أساسية - يمكن تخصيصها حسب الحاجة
        const policies = [
            'CREATE POLICY "Enable read access for all users" ON customers FOR SELECT USING (true)',
            'CREATE POLICY "Enable insert access for all users" ON customers FOR INSERT WITH CHECK (true)',
            'CREATE POLICY "Enable update access for all users" ON customers FOR UPDATE USING (true)',
            'CREATE POLICY "Enable delete access for all users" ON customers FOR DELETE USING (true)'
        ];
        
        for (const policy of policies) {
            await db.rpc('execute_sql', { sql: policy });
        }
        
        console.log('✅ تم إعداد سياسات الأمان بنجاح');
        return true;
    } catch (error) {
        console.error('❌ خطأ في إعداد سياسات الأمان:', error);
        return false;
    }
}

module.exports = {
    initializeDatabase,
    getDatabase,
    testConnection,
    createTables,
    setupSecurity,
    DATABASE_SCHEMA,
    SUPABASE_CONFIG
};
