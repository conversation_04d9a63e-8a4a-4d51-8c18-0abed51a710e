{"app": {"name": "مؤسسة وقود المستقبل", "nameEn": "Future Fuel Corporation", "version": "2.2.0", "language": "ar", "theme": "light", "autoStart": false, "minimizeToTray": true, "checkUpdates": true}, "database": {"type": "sqlite", "path": "data/futurefuel.db", "autoBackup": true, "backupInterval": 24, "maxBackups": 30, "encryptBackups": true}, "backup": {"enabled": true, "interval": 24, "maxFiles": 30, "compression": true, "encryption": true, "cloudSync": false, "telegramBackup": false, "telegramBotToken": "", "telegramChatId": ""}, "security": {"sessionTimeout": 30, "maxLoginAttempts": 3, "lockoutDuration": 15, "requireStrongPassword": true, "enableTwoFactor": false, "logSecurityEvents": true}, "ui": {"theme": "light", "language": "ar", "fontSize": "medium", "animations": true, "notifications": true, "soundEffects": false, "compactMode": false}, "printing": {"defaultPrinter": "", "paperSize": "A4", "orientation": "portrait", "margins": {"top": 20, "bottom": 20, "left": 20, "right": 20}, "includeHeader": true, "includeFooter": true, "includeLogo": true}, "reports": {"defaultFormat": "pdf", "includeCharts": true, "colorPrinting": true, "autoSave": true, "savePath": "reports/", "emailReports": false, "emailSettings": {"smtp": "", "port": 587, "username": "", "password": "", "from": "", "to": []}}, "license": {"key": "", "type": "trial", "status": "inactive", "activationDate": "", "expiryDate": "", "deviceId": "", "serverUrl": "https://license.futurefuel.sa", "checkInterval": 3600, "offlineGracePeriod": 7}, "developer": {"name": "ISHQK", "phone": "**********", "whatsapp": "**********", "email": "<EMAIL>", "website": "https://futurefuel.sa", "supportHours": "8:00-20:00"}, "features": {"gasCards": true, "customers": true, "appointments": true, "inventory": true, "sales": true, "purchases": true, "debts": true, "suppliers": true, "certificates": true, "transmission": true, "reports": true, "statistics": true, "darkMode": true, "multiUser": false, "cloudSync": false, "mobileApp": false}, "paths": {"data": "data/", "backups": "backups/", "reports": "reports/", "logs": "logs/", "temp": "temp/", "exports": "exports/", "imports": "imports/"}, "logging": {"enabled": true, "level": "info", "maxFileSize": "10MB", "maxFiles": 10, "logToFile": true, "logToConsole": true, "includeTimestamp": true, "includeLevel": true}, "performance": {"enableCache": true, "cacheSize": "100MB", "lazyLoading": true, "preloadData": false, "optimizeImages": true, "compressData": true}, "network": {"timeout": 30000, "retryAttempts": 3, "retryDelay": 1000, "useProxy": false, "proxyHost": "", "proxyPort": 8080, "proxyAuth": false, "proxyUsername": "", "proxyPassword": ""}, "updates": {"checkOnStartup": true, "autoDownload": false, "autoInstall": false, "channel": "stable", "serverUrl": "https://updates.futurefuel.sa", "checkInterval": 86400}, "integration": {"telegram": {"enabled": false, "botToken": "", "chatId": "", "notifications": false, "backups": false, "reports": false}, "email": {"enabled": false, "smtp": "", "port": 587, "security": "tls", "username": "", "password": "", "from": "", "notifications": false, "reports": false}, "sms": {"enabled": false, "provider": "", "apiKey": "", "sender": "", "notifications": false, "reminders": false}}, "customization": {"companyName": "", "companyLogo": "", "companyAddress": "", "companyPhone": "", "companyEmail": "", "companyWebsite": "", "primaryColor": "#2c3e50", "secondaryColor": "#3498db", "accentColor": "#e74c3c", "fontFamily": "Segoe UI", "fontSize": 14}, "advanced": {"debugMode": false, "verboseLogging": false, "enableExperimentalFeatures": false, "allowUnsafeOperations": false, "bypassLicenseCheck": false, "developerMode": false}}