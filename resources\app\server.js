const express = require('express');
const cors = require('cors');
const compression = require('compression');
const helmet = require('helmet');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet({
    contentSecurityPolicy: false // تعطيل CSP للسماح بالموارد الخارجية
}));
app.use(cors());
app.use(compression());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// خدمة الملفات الثابتة
app.use(express.static(path.join(__dirname)));

// مسار البيانات
const DATA_FILE = path.join(__dirname, 'data.json');
const BACKUP_DIR = path.join(__dirname, 'backups');

// إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
if (!fs.existsSync(BACKUP_DIR)) {
    fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

// API لحفظ البيانات
app.post('/api/data/save', (req, res) => {
    try {
        const data = req.body;
        fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));
        
        res.json({
            success: true,
            message: 'تم حفظ البيانات بنجاح',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('خطأ في حفظ البيانات:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حفظ البيانات',
            error: error.message
        });
    }
});

// API لتحميل البيانات
app.get('/api/data/load', (req, res) => {
    try {
        if (fs.existsSync(DATA_FILE)) {
            const data = fs.readFileSync(DATA_FILE, 'utf8');
            res.json({
                success: true,
                data: JSON.parse(data)
            });
        } else {
            res.json({
                success: true,
                data: null,
                message: 'لا توجد بيانات محفوظة'
            });
        }
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تحميل البيانات',
            error: error.message
        });
    }
});

// API لإنشاء نسخة احتياطية
app.post('/api/backup/create', (req, res) => {
    try {
        const data = req.body;
        const timestamp = new Date().getTime();
        const backupFileName = `backup_${timestamp}.json`;
        const backupPath = path.join(BACKUP_DIR, backupFileName);
        
        fs.writeFileSync(backupPath, JSON.stringify(data, null, 2));
        
        res.json({
            success: true,
            message: 'تم إنشاء النسخة الاحتياطية بنجاح',
            filename: backupFileName,
            path: backupPath,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إنشاء النسخة الاحتياطية',
            error: error.message
        });
    }
});

// API لقائمة النسخ الاحتياطية
app.get('/api/backup/list', (req, res) => {
    try {
        const backups = [];
        const files = fs.readdirSync(BACKUP_DIR);
        
        files.forEach(file => {
            if (file.endsWith('.json')) {
                const filePath = path.join(BACKUP_DIR, file);
                const stats = fs.statSync(filePath);
                backups.push({
                    filename: file,
                    size: stats.size,
                    created: stats.birthtime,
                    modified: stats.mtime
                });
            }
        });
        
        // ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
        backups.sort((a, b) => new Date(b.created) - new Date(a.created));
        
        res.json({
            success: true,
            backups: backups
        });
    } catch (error) {
        console.error('خطأ في قراءة النسخ الاحتياطية:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في قراءة النسخ الاحتياطية',
            error: error.message
        });
    }
});

// الصفحة الرئيسية
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// بدء الخادم
app.listen(PORT, () => {
    console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
    console.log(`📂 مجلد البيانات: ${__dirname}`);
    console.log(`💾 مجلد النسخ الاحتياطية: ${BACKUP_DIR}`);
    
    // فتح المتصفح تلقائياً
    if (process.env.NODE_ENV !== 'production') {
        const open = require('open');
        open(`http://localhost:${PORT}`).catch(err => {
            console.log('لا يمكن فتح المتصفح تلقائياً:', err.message);
        });
    }
});

// معالجة إغلاق الخادم
process.on('SIGINT', () => {
    console.log('\n🛑 إيقاف الخادم...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 إيقاف الخادم...');
    process.exit(0);
});
