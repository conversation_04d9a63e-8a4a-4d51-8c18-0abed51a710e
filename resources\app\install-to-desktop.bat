@echo off
chcp 65001 >nul
title تثبيت مؤسسة وقود المستقبل على سطح المكتب

echo ========================================
echo    تثبيت مؤسسة وقود المستقبل
echo    على سطح المكتب
echo ========================================
echo.

echo اختر طريقة التثبيت:
echo.
echo [1] إنشاء اختصار فقط (سريع)
echo [2] تثبيت كامل مع بناء التطبيق
echo [3] نسخ التطبيق إلى مجلد البرامج
echo [4] تثبيت شامل (جميع الخيارات)
echo.
set /p install_choice=اختر رقم (1-4): 

if "%install_choice%"=="1" (
    echo.
    echo 🔗 إنشاء اختصار سطح المكتب...
    call create-desktop-shortcut.bat
    goto :end
)

if "%install_choice%"=="2" (
    echo.
    echo 🔨 بناء التطبيق أولاً...
    call build-app.bat
    if %errorlevel% neq 0 (
        echo ❌ فشل في بناء التطبيق
        pause
        exit /b 1
    )
    echo.
    echo 🔗 إنشاء اختصار سطح المكتب...
    call create-desktop-shortcut.bat
    goto :end
)

if "%install_choice%"=="3" (
    echo.
    echo 📁 نسخ التطبيق إلى مجلد البرامج...
    
    REM Create program folder
    set "PROGRAM_DIR=%PROGRAMFILES%\مؤسسة وقود المستقبل"
    if not exist "%PROGRAM_DIR%" (
        mkdir "%PROGRAM_DIR%" 2>nul
        if %errorlevel% neq 0 (
            echo ❌ فشل في إنشاء مجلد البرامج
            echo يرجى تشغيل الملف كمدير
            pause
            exit /b 1
        )
    )
    
    echo نسخ الملفات...
    xcopy /E /I /Y "%cd%" "%PROGRAM_DIR%" >nul
    if %errorlevel% neq 0 (
        echo ❌ فشل في نسخ الملفات
        pause
        exit /b 1
    )
    
    echo ✅ تم نسخ التطبيق بنجاح
    
    REM Create desktop shortcut pointing to program folder
    echo إنشاء اختصار سطح المكتب...
    
    for /f "tokens=3*" %%i in ('reg query "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\User Shell Folders" /v Desktop 2^>nul') do set "DESKTOP_PATH=%%j"
    if "%DESKTOP_PATH%"=="" set "DESKTOP_PATH=%USERPROFILE%\Desktop"
    call set "DESKTOP_PATH=%DESKTOP_PATH%"
    
    echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateProgramShortcut.vbs"
    echo sLinkFile = "%DESKTOP_PATH%\مؤسسة وقود المستقبل.lnk" >> "%TEMP%\CreateProgramShortcut.vbs"
    echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateProgramShortcut.vbs"
    echo oLink.TargetPath = "%PROGRAM_DIR%\run-app.bat" >> "%TEMP%\CreateProgramShortcut.vbs"
    echo oLink.WorkingDirectory = "%PROGRAM_DIR%" >> "%TEMP%\CreateProgramShortcut.vbs"
    echo oLink.Description = "نظام إدارة مؤسسة وقود المستقبل" >> "%TEMP%\CreateProgramShortcut.vbs"
    echo oLink.IconLocation = "%PROGRAM_DIR%\icons\app-icon.ico" >> "%TEMP%\CreateProgramShortcut.vbs"
    echo oLink.Save >> "%TEMP%\CreateProgramShortcut.vbs"
    
    cscript //nologo "%TEMP%\CreateProgramShortcut.vbs"
    del "%TEMP%\CreateProgramShortcut.vbs"
    
    echo ✅ تم إنشاء اختصار سطح المكتب
    goto :end
)

if "%install_choice%"=="4" (
    echo.
    echo 🚀 تثبيت شامل...
    echo.
    
    echo [1/4] فحص وتثبيت التبعيات...
    call install-electron.bat
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
    
    echo.
    echo [2/4] بناء التطبيق...
    call build-app.bat
    if %errorlevel% neq 0 (
        echo ❌ فشل في بناء التطبيق
        pause
        exit /b 1
    )
    
    echo.
    echo [3/4] نسخ إلى مجلد البرامج...
    set "PROGRAM_DIR=%PROGRAMFILES%\مؤسسة وقود المستقبل"
    if not exist "%PROGRAM_DIR%" mkdir "%PROGRAM_DIR%" 2>nul
    xcopy /E /I /Y "%cd%" "%PROGRAM_DIR%" >nul
    
    echo.
    echo [4/4] إنشاء الاختصارات...
    call create-desktop-shortcut.bat
    
    echo.
    echo ✅ تم التثبيت الشامل بنجاح!
    goto :end
) else (
    echo ❌ اختيار غير صحيح
    pause
    exit /b 1
)

:end
echo.
echo ========================================
echo تم الانتهاء من التثبيت!
echo.
echo يمكنك الآن:
echo • النقر المزدوج على أيقونة سطح المكتب
echo • البحث عن "مؤسسة وقود المستقبل" في قائمة ابدأ
echo • تشغيل التطبيق من مجلد البرامج
echo ========================================
echo.
echo هل تريد تشغيل التطبيق الآن؟ (y/n)
set /p run_now=
if /i "%run_now%"=="y" (
    echo.
    echo 🚀 تشغيل التطبيق...
    call run-app.bat
)

pause
