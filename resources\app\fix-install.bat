@echo off
title حل مشكلة تثبيت Electron - Future Fuel Corporation

echo ========================================
echo    حل مشكلة تثبيت Electron
echo    Future Fuel Corporation
echo ========================================
echo.

echo [1/6] إيقاف جميع العمليات المتعلقة...
taskkill /F /IM electron.exe 2>nul
taskkill /F /IM node.exe 2>nul
taskkill /F /IM npm.exe 2>nul
echo تم إيقاف العمليات

echo.
echo [2/6] انتظار 3 ثوان...
timeout /t 3 /nobreak >nul

echo.
echo [3/6] حذف مجلد node_modules...
if exist "node_modules" (
    echo جاري حذف node_modules...
    rmdir /s /q node_modules
    if exist "node_modules" (
        echo تعذر حذف node_modules، جرب إعادة تشغيل الكمبيوتر
        pause
        exit /b 1
    )
    echo تم حذف node_modules بنجاح
) else (
    echo node_modules غير موجود
)

echo.
echo [4/6] حذف ملفات القفل...
del package-lock.json 2>nul
del yarn.lock 2>nul
echo تم حذف ملفات القفل

echo.
echo [5/6] تنظيف cache...
npm cache clean --force
echo تم تنظيف cache

echo.
echo [6/6] تثبيت التبعيات الأساسية فقط...
echo جاري تثبيت Electron...

REM تثبيت Electron بدون postinstall scripts
npm install electron --save-dev --ignore-scripts

if %errorlevel% equ 0 (
    echo ✅ تم تثبيت Electron بنجاح!
    echo.
    echo الآن يمكنك تشغيل:
    echo   npm start
    echo.
) else (
    echo ❌ فشل في تثبيت Electron
    echo.
    echo الحلول البديلة:
    echo 1. أعد تشغيل الكمبيوتر وجرب مرة أخرى
    echo 2. تأكد من عدم وجود برامج مكافحة فيروسات تحجب npm
    echo 3. شغل هذا الملف كمدير (Run as Administrator)
    echo.
)

pause
