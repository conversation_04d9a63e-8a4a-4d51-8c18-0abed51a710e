// إصلاح نظام ترحيل البيانات
// Data Migration System Fix

class MigrationFix {
    constructor() {
        this.migrationStatus = {
            isRunning: false,
            currentStep: '',
            progress: 0,
            errors: [],
            warnings: [],
            migratedCounts: {},
            completed: false
        };
        this.oldData = null;
        this.transformedData = null;
    }

    /**
     * بدء عملية الترحيل المحسنة
     */
    async startMigration() {
        try {
            console.log('🚀 بدء عملية ترحيل البيانات المحسنة...');
            
            this.migrationStatus.isRunning = true;
            this.migrationStatus.progress = 0;
            this.migrationStatus.errors = [];
            this.migrationStatus.warnings = [];
            this.migrationStatus.migratedCounts = {};

            // التحقق من وجود بيانات للترحيل
            if (!this.checkForOldData()) {
                console.log('ℹ️ لا توجد بيانات قديمة للترحيل');
                this.migrationStatus.isRunning = false;
                return false;
            }

            // الخطوة 1: تحميل البيانات القديمة
            await this.loadOldData();

            // الخطوة 2: التحقق من صحة البيانات
            await this.validateData();

            // الخطوة 3: تحويل البيانات
            await this.transformData();

            // الخطوة 4: ترحيل البيانات
            await this.migrateAllData();

            // الخطوة 5: إنشاء نسخة احتياطية
            await this.backupOldData();

            // الخطوة 6: وضع علامة الإكمال
            this.markMigrationComplete();

            this.migrationStatus.isRunning = false;
            this.migrationStatus.progress = 100;
            this.migrationStatus.completed = true;
            
            console.log('✅ تمت عملية ترحيل البيانات بنجاح');
            this.showMigrationReport();

            return true;

        } catch (error) {
            console.error('❌ خطأ في عملية ترحيل البيانات:', error);
            this.migrationStatus.isRunning = false;
            this.migrationStatus.errors.push(error.message);
            return false;
        }
    }

    /**
     * التحقق من وجود بيانات قديمة
     */
    checkForOldData() {
        // التحقق من localStorage
        const gasShopData = localStorage.getItem('gasShopData');
        const customersData = localStorage.getItem('customersData');
        
        // التحقق من appData
        const hasAppData = window.appData && (
            (window.appData.customers && window.appData.customers.length > 0) ||
            (window.appData.vehicles && window.appData.vehicles.length > 0) ||
            (window.appData.gasCards && window.appData.gasCards.length > 0)
        );

        return gasShopData || customersData || hasAppData;
    }

    /**
     * تحميل البيانات القديمة
     */
    async loadOldData() {
        this.updateProgress('تحميل البيانات القديمة...', 10);
        
        try {
            this.oldData = {
                customers: [],
                vehicles: [],
                gasTanks: [],
                gasCards: [],
                appointments: [],
                debts: [],
                transmissionTable: []
            };

            // تحميل من gasShopData
            const gasShopData = localStorage.getItem('gasShopData');
            if (gasShopData) {
                const data = JSON.parse(gasShopData);
                this.mergeData(this.oldData, data);
                console.log('✅ تم تحميل البيانات من gasShopData');
            }

            // تحميل من customersData
            const customersData = localStorage.getItem('customersData');
            if (customersData) {
                const customers = JSON.parse(customersData);
                if (Array.isArray(customers)) {
                    this.oldData.customers = [...this.oldData.customers, ...customers];
                }
                console.log('✅ تم تحميل البيانات من customersData');
            }

            // تحميل من appData
            if (window.appData) {
                this.mergeData(this.oldData, window.appData);
                console.log('✅ تم تحميل البيانات من appData');
            }

            console.log('📊 إجمالي البيانات المحملة:', {
                customers: this.oldData.customers.length,
                vehicles: this.oldData.vehicles.length,
                gasTanks: this.oldData.gasTanks.length,
                gasCards: this.oldData.gasCards.length,
                appointments: this.oldData.appointments.length,
                debts: this.oldData.debts.length,
                transmissionTable: this.oldData.transmissionTable.length
            });

        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات القديمة:', error);
            throw error;
        }
    }

    /**
     * دمج البيانات
     */
    mergeData(target, source) {
        const dataTypes = ['customers', 'vehicles', 'gasTanks', 'gasCards', 'appointments', 'debts', 'transmissionTable'];
        
        dataTypes.forEach(type => {
            if (source[type] && Array.isArray(source[type])) {
                if (!target[type]) target[type] = [];
                
                // تجنب التكرار
                source[type].forEach(item => {
                    const exists = target[type].find(existing => 
                        existing.id === item.id || 
                        (existing.phone === item.phone && type === 'customers') ||
                        (existing.plateNumber === item.plateNumber && type === 'vehicles')
                    );
                    
                    if (!exists) {
                        target[type].push(item);
                    }
                });
            }
        });
    }

    /**
     * التحقق من صحة البيانات
     */
    async validateData() {
        this.updateProgress('التحقق من صحة البيانات...', 20);
        
        try {
            // تنظيف بيانات الزبائن
            this.oldData.customers = this.oldData.customers.filter(customer => {
                if (!customer.name || !customer.phone) {
                    this.migrationStatus.warnings.push(`زبون بدون اسم أو هاتف تم تجاهله`);
                    return false;
                }
                return true;
            });

            // تنظيف بيانات السيارات
            this.oldData.vehicles = this.oldData.vehicles.filter(vehicle => {
                if (!vehicle.plateNumber) {
                    this.migrationStatus.warnings.push(`سيارة بدون رقم لوحة تم تجاهلها`);
                    return false;
                }
                return true;
            });

            console.log('✅ تم التحقق من صحة البيانات');

        } catch (error) {
            console.error('❌ خطأ في التحقق من البيانات:', error);
            throw error;
        }
    }

    /**
     * تحويل البيانات إلى الصيغة الجديدة
     */
    async transformData() {
        this.updateProgress('تحويل البيانات إلى الصيغة الجديدة...', 30);
        
        try {
            this.transformedData = {
                customers: this.transformCustomers(),
                vehicles: this.transformVehicles(),
                gasTanks: this.transformGasTanks(),
                gasCards: this.transformGasCards(),
                appointments: this.transformAppointments(),
                debts: this.transformDebts(),
                transmissionTable: this.transformTransmissionTable()
            };

            console.log('✅ تم تحويل البيانات بنجاح');

        } catch (error) {
            console.error('❌ خطأ في تحويل البيانات:', error);
            throw error;
        }
    }

    /**
     * تحويل بيانات الزبائن
     */
    transformCustomers() {
        return this.oldData.customers.map(customer => ({
            id: customer.id || this.generateUUID(),
            name: customer.name.trim(),
            phone: customer.phone.trim(),
            email: customer.email ? customer.email.trim() : null,
            address: customer.address ? customer.address.trim() : null,
            notes: customer.notes ? customer.notes.trim() : null,
            created_at: customer.createdAt || customer.created_at || new Date().toISOString(),
            updated_at: customer.updatedAt || customer.updated_at || new Date().toISOString(),
            is_active: customer.is_active !== false
        }));
    }

    /**
     * تحويل بيانات السيارات
     */
    transformVehicles() {
        return this.oldData.vehicles.map(vehicle => ({
            id: vehicle.id || this.generateUUID(),
            customer_id: vehicle.customerId || vehicle.customer_id,
            plate_number: vehicle.plateNumber || vehicle.plate_number,
            brand: vehicle.brand || vehicle.vehicleBrand || '',
            model: vehicle.model || vehicle.vehicleModel || '',
            year: vehicle.year || vehicle.vehicleYear || null,
            color: vehicle.color || vehicle.vehicleColor || null,
            chassis_number: vehicle.chassisNumber || vehicle.chassis_number || null,
            engine_number: vehicle.engineNumber || vehicle.engine_number || null,
            created_at: vehicle.createdAt || vehicle.created_at || new Date().toISOString(),
            updated_at: vehicle.updatedAt || vehicle.updated_at || new Date().toISOString(),
            is_active: vehicle.is_active !== false
        }));
    }

    /**
     * تحويل بيانات خزانات الغاز
     */
    transformGasTanks() {
        return this.oldData.gasTanks.map(tank => ({
            id: tank.id || this.generateUUID(),
            vehicle_id: tank.vehicleId || tank.vehicle_id,
            tank_type: tank.tankType || tank.tank_type || tank.type || '',
            tank_brand: tank.tankBrand || tank.tank_brand || tank.brand || '',
            serial_number: tank.tankSerialNumber || tank.serial_number || tank.serialNumber || '',
            capacity: parseFloat(tank.tankCapacity || tank.capacity) || 0,
            manufacturing_date: tank.manufacturingDate || tank.manufacturing_date || null,
            installation_date: tank.installationDate || tank.installation_date || null,
            last_inspection_date: tank.lastInspectionDate || tank.last_inspection_date || null,
            next_inspection_date: tank.nextInspectionDate || tank.next_inspection_date || null,
            created_at: tank.createdAt || tank.created_at || new Date().toISOString(),
            updated_at: tank.updatedAt || tank.updated_at || new Date().toISOString(),
            is_active: tank.is_active !== false
        }));
    }

    /**
     * تحويل بيانات بطاقات الغاز
     */
    transformGasCards() {
        return this.oldData.gasCards.map(card => ({
            id: card.id || this.generateUUID(),
            customer_id: card.customerId || card.customer_id,
            vehicle_id: card.vehicleId || card.vehicle_id,
            card_number: card.cardNumber || card.card_number || '',
            issue_date: card.issueDate || card.issue_date || new Date().toISOString().split('T')[0],
            expiry_date: card.expiryDate || card.expiry_date || this.calculateExpiryDate(card.issueDate),
            status: card.status || 'active',
            created_at: card.createdAt || card.created_at || new Date().toISOString(),
            updated_at: card.updatedAt || card.updated_at || new Date().toISOString()
        }));
    }

    /**
     * تحويل بيانات المواعيد
     */
    transformAppointments() {
        return this.oldData.appointments.map(appointment => ({
            id: appointment.id || this.generateUUID(),
            customer_id: appointment.customerId || appointment.customer_id,
            vehicle_id: appointment.vehicleId || appointment.vehicle_id,
            appointment_date: appointment.appointmentDate || appointment.appointment_date || new Date().toISOString(),
            service_type: appointment.serviceType || appointment.service_type || '',
            status: appointment.status || 'scheduled',
            notes: appointment.notes || null,
            created_at: appointment.createdAt || appointment.created_at || new Date().toISOString(),
            updated_at: appointment.updatedAt || appointment.updated_at || new Date().toISOString()
        }));
    }

    /**
     * تحويل بيانات الديون
     */
    transformDebts() {
        return this.oldData.debts.map(debt => ({
            id: debt.id || this.generateUUID(),
            customer_id: debt.customerId || debt.customer_id,
            amount: parseFloat(debt.amount) || 0,
            description: debt.description || null,
            due_date: debt.dueDate || debt.due_date || null,
            status: debt.status || 'pending',
            created_at: debt.createdAt || debt.created_at || new Date().toISOString(),
            updated_at: debt.updatedAt || debt.updated_at || new Date().toISOString()
        }));
    }

    /**
     * تحويل بيانات جدول الإرسال
     */
    transformTransmissionTable() {
        return this.oldData.transmissionTable.map(entry => ({
            id: entry.id || this.generateUUID(),
            type: entry.type || '',
            tank_number: entry.tankNumber || entry.tank_number || '',
            car_type: entry.carType || entry.car_type || '',
            serial_number: entry.serialNumber || entry.serial_number || '',
            registration_number: entry.registrationNumber || entry.registration_number || '',
            owner_name: entry.ownerName || entry.owner_name || '',
            phone_number: entry.phoneNumber || entry.phone_number || '',
            operation_date: entry.operationDate || entry.operation_date || new Date().toISOString().split('T')[0],
            notes: entry.notes || null,
            status: entry.status || 'مكتمل',
            created_at: entry.createdAt || entry.created_at || new Date().toISOString(),
            updated_at: entry.updatedAt || entry.updated_at || new Date().toISOString()
        }));
    }

    /**
     * ترحيل جميع البيانات
     */
    async migrateAllData() {
        this.updateProgress('ترحيل البيانات...', 50);
        
        try {
            // التأكد من وجود appData
            if (!window.appData) {
                window.appData = {};
            }

            // ترحيل البيانات مباشرة إلى appData
            window.appData.customers = this.transformedData.customers;
            window.appData.vehicles = this.transformedData.vehicles;
            window.appData.gasTanks = this.transformedData.gasTanks;
            window.appData.gasCards = this.transformedData.gasCards;
            window.appData.appointments = this.transformedData.appointments;
            window.appData.debts = this.transformedData.debts;
            window.appData.transmissionTable = this.transformedData.transmissionTable;

            // حفظ البيانات
            if (typeof saveData === 'function') {
                saveData();
            }

            // تحديث العدادات
            this.migrationStatus.migratedCounts = {
                customers: this.transformedData.customers.length,
                vehicles: this.transformedData.vehicles.length,
                gasTanks: this.transformedData.gasTanks.length,
                gasCards: this.transformedData.gasCards.length,
                appointments: this.transformedData.appointments.length,
                debts: this.transformedData.debts.length,
                transmissionTable: this.transformedData.transmissionTable.length
            };

            // تحديث الجداول
            if (typeof updateCustomersTable === 'function') {
                updateCustomersTable();
            }
            if (typeof updateGasCardsTable === 'function') {
                updateGasCardsTable();
            }
            if (typeof updateTransmissionTable === 'function') {
                updateTransmissionTable();
            }

            console.log('✅ تم ترحيل جميع البيانات بنجاح');

        } catch (error) {
            console.error('❌ خطأ في ترحيل البيانات:', error);
            throw error;
        }
    }

    /**
     * إنشاء نسخة احتياطية من البيانات القديمة
     */
    async backupOldData() {
        this.updateProgress('إنشاء نسخة احتياطية...', 90);
        
        try {
            const backupData = {
                originalData: this.oldData,
                migrationDate: new Date().toISOString(),
                migrationStatus: this.migrationStatus,
                version: '2.2.0'
            };

            const backupName = `migration_backup_${new Date().toISOString().split('T')[0]}_${Date.now()}`;
            localStorage.setItem(backupName, JSON.stringify(backupData));

            console.log('✅ تم إنشاء نسخة احتياطية من البيانات القديمة');

        } catch (error) {
            console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
            this.migrationStatus.warnings.push('فشل في إنشاء نسخة احتياطية من البيانات القديمة');
        }
    }

    /**
     * وضع علامة إكمال الترحيل
     */
    markMigrationComplete() {
        localStorage.setItem('dataMigrationCompleted', JSON.stringify({
            completed: true,
            date: new Date().toISOString(),
            version: '2.2.0'
        }));
    }

    /**
     * تحديث التقدم
     */
    updateProgress(step, progress) {
        this.migrationStatus.currentStep = step;
        this.migrationStatus.progress = progress;
        console.log(`📊 ${step} (${progress}%)`);
    }

    /**
     * عرض تقرير الترحيل
     */
    showMigrationReport() {
        const report = `
=== تقرير ترحيل البيانات ===
✅ تم ترحيل ${this.migrationStatus.migratedCounts.customers || 0} زبون
✅ تم ترحيل ${this.migrationStatus.migratedCounts.vehicles || 0} سيارة
✅ تم ترحيل ${this.migrationStatus.migratedCounts.gasTanks || 0} خزان غاز
✅ تم ترحيل ${this.migrationStatus.migratedCounts.gasCards || 0} بطاقة غاز
✅ تم ترحيل ${this.migrationStatus.migratedCounts.appointments || 0} موعد
✅ تم ترحيل ${this.migrationStatus.migratedCounts.debts || 0} دين
✅ تم ترحيل ${this.migrationStatus.migratedCounts.transmissionTable || 0} عملية إرسال

⚠️ تحذيرات: ${this.migrationStatus.warnings.length}
❌ أخطاء: ${this.migrationStatus.errors.length}
        `;

        console.log(report);
        
        if (typeof showToast === 'function') {
            showToast('تمت عملية ترحيل البيانات بنجاح', true);
        }
    }

    /**
     * حساب تاريخ انتهاء البطاقة
     */
    calculateExpiryDate(issueDate) {
        const issue = new Date(issueDate || new Date());
        const expiry = new Date(issue);
        expiry.setFullYear(expiry.getFullYear() + 1);
        return expiry.toISOString().split('T')[0];
    }

    /**
     * توليد UUID
     */
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    /**
     * الحصول على حالة الترحيل
     */
    getStatus() {
        return this.migrationStatus;
    }

    /**
     * التحقق من إكمال الترحيل
     */
    isMigrationCompleted() {
        const completed = localStorage.getItem('dataMigrationCompleted');
        return completed ? JSON.parse(completed).completed : false;
    }
}

// إنشاء مثيل من نظام الترحيل المحسن
const migrationFix = new MigrationFix();

// تصدير النظام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = migrationFix;
} else {
    window.migrationFix = migrationFix;
}

// تشغيل الترحيل تلقائياً إذا لم يكن مكتملاً
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(async () => {
            if (!migrationFix.isMigrationCompleted() && migrationFix.checkForOldData()) {
                console.log('🔄 بدء الترحيل التلقائي...');
                await migrationFix.startMigration();
            }
        }, 3000);
    });
}
