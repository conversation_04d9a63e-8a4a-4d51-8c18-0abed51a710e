// نظام ترحيل البيانات المبسط والفعال
// Simplified and Effective Data Migration System

class MigrationFix {
    constructor() {
        this.migrationStatus = {
            isRunning: false,
            currentStep: '',
            progress: 0,
            errors: [],
            warnings: [],
            migratedCounts: {},
            completed: false
        };
        this.oldData = null;
    }

    /**
     * بدء عملية الترحيل المبسطة
     */
    async startMigration() {
        try {
            console.log('🚀 بدء عملية ترحيل البيانات...');

            this.migrationStatus.isRunning = true;
            this.migrationStatus.progress = 0;
            this.migrationStatus.errors = [];
            this.migrationStatus.warnings = [];
            this.migrationStatus.migratedCounts = {};

            // التحقق من وجود بيانات للترحيل
            if (!this.checkForOldData()) {
                console.log('ℹ️ لا توجد بيانات قديمة للترحيل');
                this.migrationStatus.isRunning = false;
                return false;
            }

            // تحميل وترحيل البيانات مباشرة
            await this.loadAndMigrateData();

            // وضع علامة الإكمال
            this.markMigrationComplete();

            this.migrationStatus.isRunning = false;
            this.migrationStatus.progress = 100;
            this.migrationStatus.completed = true;

            console.log('✅ تمت عملية ترحيل البيانات بنجاح');
            this.showMigrationReport();

            return true;

        } catch (error) {
            console.error('❌ خطأ في عملية ترحيل البيانات:', error);
            this.migrationStatus.isRunning = false;
            this.migrationStatus.errors.push(error.message);
            return false;
        }
    }

    /**
     * التحقق من وجود بيانات قديمة
     */
    checkForOldData() {
        // التحقق من localStorage
        const gasShopData = localStorage.getItem('gasShopData');
        const customersData = localStorage.getItem('customersData');
        
        // التحقق من appData
        const hasAppData = window.appData && (
            (window.appData.customers && window.appData.customers.length > 0) ||
            (window.appData.vehicles && window.appData.vehicles.length > 0) ||
            (window.appData.gasCards && window.appData.gasCards.length > 0)
        );

        return gasShopData || customersData || hasAppData;
    }

    /**
     * تحميل وترحيل البيانات مباشرة
     */
    async loadAndMigrateData() {
        this.updateProgress('تحميل وترحيل البيانات...', 20);

        try {
            // التأكد من وجود appData
            if (!window.appData) {
                window.appData = {
                    customers: [],
                    vehicles: [],
                    gasTanks: [],
                    gasCards: [],
                    appointments: [],
                    debts: [],
                    transmissionTable: []
                };
            }

            let migratedCount = 0;

            // ترحيل من gasShopData
            const gasShopData = localStorage.getItem('gasShopData');
            if (gasShopData) {
                const data = JSON.parse(gasShopData);
                migratedCount += this.migrateFromSource(data, 'gasShopData');
            }

            // ترحيل من customersData
            const customersData = localStorage.getItem('customersData');
            if (customersData) {
                const customers = JSON.parse(customersData);
                if (Array.isArray(customers)) {
                    migratedCount += this.migrateCustomersArray(customers, 'customersData');
                }
            }

            // حفظ البيانات
            if (typeof saveData === 'function') {
                saveData();
            }

            // تحديث الجداول
            this.updateAllTables();

            console.log(`✅ تم ترحيل ${migratedCount} عنصر بنجاح`);

        } catch (error) {
            console.error('❌ خطأ في تحميل وترحيل البيانات:', error);
            throw error;
        }
    }

    /**
     * ترحيل من مصدر بيانات
     */
    migrateFromSource(data, sourceName) {
        let count = 0;

        try {
            // ترحيل الزبائن
            if (data.customers && Array.isArray(data.customers)) {
                data.customers.forEach(customer => {
                    if (this.addCustomerIfNotExists(customer)) count++;
                });
            }

            // ترحيل السيارات
            if (data.vehicles && Array.isArray(data.vehicles)) {
                data.vehicles.forEach(vehicle => {
                    if (this.addVehicleIfNotExists(vehicle)) count++;
                });
            }

            // ترحيل خزانات الغاز
            if (data.gasTanks && Array.isArray(data.gasTanks)) {
                data.gasTanks.forEach(tank => {
                    if (this.addGasTankIfNotExists(tank)) count++;
                });
            }

            // ترحيل بطاقات الغاز
            if (data.gasCards && Array.isArray(data.gasCards)) {
                data.gasCards.forEach(card => {
                    if (this.addGasCardIfNotExists(card)) count++;
                });
            }

            // ترحيل جدول الإرسال
            if (data.transmissionTable && Array.isArray(data.transmissionTable)) {
                data.transmissionTable.forEach(entry => {
                    if (this.addTransmissionEntryIfNotExists(entry)) count++;
                });
            }

            console.log(`✅ تم ترحيل ${count} عنصر من ${sourceName}`);
            return count;

        } catch (error) {
            console.error(`❌ خطأ في ترحيل البيانات من ${sourceName}:`, error);
            return 0;
        }
    }

    /**
     * ترحيل مصفوفة الزبائن
     */
    migrateCustomersArray(customers, sourceName) {
        let count = 0;

        try {
            customers.forEach(customer => {
                if (this.addCustomerIfNotExists(customer)) count++;
            });

            console.log(`✅ تم ترحيل ${count} زبون من ${sourceName}`);
            return count;

        } catch (error) {
            console.error(`❌ خطأ في ترحيل الزبائن من ${sourceName}:`, error);
            return 0;
        }
    }

    /**
     * إضافة زبون إذا لم يكن موجوداً
     */
    addCustomerIfNotExists(customer) {
        if (!customer.name || !customer.phone) return false;

        const exists = window.appData.customers.find(c =>
            c.phone === customer.phone ||
            (c.id && c.id === customer.id)
        );

        if (!exists) {
            const newCustomer = {
                id: customer.id || this.generateUUID(),
                name: customer.name.trim(),
                phone: customer.phone.trim(),
                email: customer.email || null,
                address: customer.address || null,
                notes: customer.notes || null,
                createdAt: customer.createdAt || customer.created_at || new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            window.appData.customers.push(newCustomer);
            this.migrationStatus.migratedCounts.customers = (this.migrationStatus.migratedCounts.customers || 0) + 1;
            return true;
        }
        return false;
    }

    /**
     * إضافة سيارة إذا لم تكن موجودة
     */
    addVehicleIfNotExists(vehicle) {
        if (!vehicle.plateNumber && !vehicle.plate_number) return false;

        const plateNumber = vehicle.plateNumber || vehicle.plate_number;
        const exists = window.appData.vehicles.find(v =>
            v.plateNumber === plateNumber ||
            (v.id && v.id === vehicle.id)
        );

        if (!exists) {
            const newVehicle = {
                id: vehicle.id || this.generateUUID(),
                customerId: vehicle.customerId || vehicle.customer_id,
                plateNumber: plateNumber,
                brand: vehicle.brand || vehicle.vehicleBrand || '',
                model: vehicle.model || vehicle.vehicleModel || '',
                year: vehicle.year || vehicle.vehicleYear || null,
                color: vehicle.color || vehicle.vehicleColor || null,
                chassisNumber: vehicle.chassisNumber || vehicle.chassis_number || null,
                createdAt: vehicle.createdAt || vehicle.created_at || new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            window.appData.vehicles.push(newVehicle);
            this.migrationStatus.migratedCounts.vehicles = (this.migrationStatus.migratedCounts.vehicles || 0) + 1;
            return true;
        }
        return false;
    }

    /**
     * إضافة خزان غاز إذا لم يكن موجوداً
     */
    addGasTankIfNotExists(tank) {
        const serialNumber = tank.tankSerialNumber || tank.serial_number || tank.serialNumber;
        if (!serialNumber) return false;

        const exists = window.appData.gasTanks.find(t =>
            t.tankSerialNumber === serialNumber ||
            t.serialNumber === serialNumber ||
            (t.id && t.id === tank.id)
        );

        if (!exists) {
            const newTank = {
                id: tank.id || this.generateUUID(),
                vehicleId: tank.vehicleId || tank.vehicle_id,
                tankType: tank.tankType || tank.tank_type || tank.type || '',
                tankBrand: tank.tankBrand || tank.tank_brand || tank.brand || '',
                tankSerialNumber: serialNumber,
                tankCapacity: parseFloat(tank.tankCapacity || tank.capacity) || 0,
                manufactureDate: tank.manufactureDate || tank.manufacturing_date || null,
                createdAt: tank.createdAt || tank.created_at || new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            window.appData.gasTanks.push(newTank);
            this.migrationStatus.migratedCounts.gasTanks = (this.migrationStatus.migratedCounts.gasTanks || 0) + 1;
            return true;
        }
        return false;
    }

    /**
     * إضافة بطاقة غاز إذا لم تكن موجودة
     */
    addGasCardIfNotExists(card) {
        const cardNumber = card.cardNumber || card.card_number;
        if (!cardNumber) return false;

        const exists = window.appData.gasCards.find(c =>
            c.cardNumber === cardNumber ||
            (c.id && c.id === card.id)
        );

        if (!exists) {
            const newCard = {
                id: card.id || this.generateUUID(),
                customerId: card.customerId || card.customer_id,
                vehicleId: card.vehicleId || card.vehicle_id,
                cardNumber: cardNumber,
                vehicleNumber: card.vehicleNumber || card.vehicle_number || '',
                customerName: card.customerName || card.customer_name || '',
                tankNumber: card.tankNumber || card.tank_number || '',
                serialNumber: card.serialNumber || card.serial_number || '',
                issueDate: card.issueDate || card.issue_date || new Date().toISOString().split('T')[0],
                expiryDate: card.expiryDate || card.expiry_date || this.calculateExpiryDate(card.issueDate),
                status: card.status || 'نشطة',
                notes: card.notes || null,
                createdAt: card.createdAt || card.created_at || new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            window.appData.gasCards.push(newCard);
            this.migrationStatus.migratedCounts.gasCards = (this.migrationStatus.migratedCounts.gasCards || 0) + 1;
            return true;
        }
        return false;
    }

    /**
     * إضافة عملية إرسال إذا لم تكن موجودة
     */
    addTransmissionEntryIfNotExists(entry) {
        if (!entry.ownerName && !entry.owner_name) return false;

        const exists = window.appData.transmissionTable.find(e =>
            (e.id && e.id === entry.id) ||
            (e.ownerName === (entry.ownerName || entry.owner_name) &&
             e.registrationNumber === (entry.registrationNumber || entry.registration_number))
        );

        if (!exists) {
            const newEntry = {
                id: entry.id || this.generateUUID(),
                type: entry.type || '',
                tankNumber: entry.tankNumber || entry.tank_number || '',
                carType: entry.carType || entry.car_type || '',
                serialNumber: entry.serialNumber || entry.serial_number || '',
                registrationNumber: entry.registrationNumber || entry.registration_number || '',
                ownerName: entry.ownerName || entry.owner_name || '',
                phoneNumber: entry.phoneNumber || entry.phone_number || '',
                operationDate: entry.operationDate || entry.operation_date || new Date().toISOString().split('T')[0],
                notes: entry.notes || null,
                status: entry.status || 'مكتمل',
                createdAt: entry.createdAt || entry.created_at || new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            window.appData.transmissionTable.push(newEntry);
            this.migrationStatus.migratedCounts.transmissionTable = (this.migrationStatus.migratedCounts.transmissionTable || 0) + 1;
            return true;
        }
        return false;
    }

    /**
     * تحديث جميع الجداول
     */
    updateAllTables() {
        try {
            if (typeof updateCustomersTable === 'function') {
                updateCustomersTable();
            }
            if (typeof updateGasCardsTable === 'function') {
                updateGasCardsTable();
            }
            if (typeof updateTransmissionTable === 'function') {
                updateTransmissionTable();
            }
            console.log('✅ تم تحديث جميع الجداول');
        } catch (error) {
            console.error('❌ خطأ في تحديث الجداول:', error);
        }
    }

    /**
     * إنشاء نسخة احتياطية من البيانات القديمة
     */
    async backupOldData() {
        try {
            const backupData = {
                migrationDate: new Date().toISOString(),
                migrationStatus: this.migrationStatus,
                version: '2.2.0'
            };

            const backupName = `migration_backup_${new Date().toISOString().split('T')[0]}_${Date.now()}`;
            localStorage.setItem(backupName, JSON.stringify(backupData));

            console.log('✅ تم إنشاء نسخة احتياطية');

        } catch (error) {
            console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
        }
    }

    /**
     * وضع علامة إكمال الترحيل
     */
    markMigrationComplete() {
        localStorage.setItem('dataMigrationCompleted', JSON.stringify({
            completed: true,
            date: new Date().toISOString(),
            version: '2.2.0'
        }));
    }

    /**
     * تحديث التقدم
     */
    updateProgress(step, progress) {
        this.migrationStatus.currentStep = step;
        this.migrationStatus.progress = progress;
        console.log(`📊 ${step} (${progress}%)`);
    }

    /**
     * عرض تقرير الترحيل
     */
    showMigrationReport() {
        const counts = this.migrationStatus.migratedCounts;
        const total = Object.values(counts).reduce((sum, count) => sum + count, 0);

        console.log('=== تقرير ترحيل البيانات ===');
        console.log(`✅ تم ترحيل ${counts.customers || 0} زبون`);
        console.log(`✅ تم ترحيل ${counts.vehicles || 0} سيارة`);
        console.log(`✅ تم ترحيل ${counts.gasTanks || 0} خزان غاز`);
        console.log(`✅ تم ترحيل ${counts.gasCards || 0} بطاقة غاز`);
        console.log(`✅ تم ترحيل ${counts.transmissionTable || 0} عملية إرسال`);
        console.log(`📊 إجمالي العناصر المرحلة: ${total}`);

        if (typeof showToast === 'function') {
            showToast(`تم ترحيل ${total} عنصر بنجاح`, true);
        }
    }

    /**
     * حساب تاريخ انتهاء البطاقة
     */
    calculateExpiryDate(issueDate) {
        const issue = new Date(issueDate || new Date());
        const expiry = new Date(issue);
        expiry.setFullYear(expiry.getFullYear() + 1);
        return expiry.toISOString().split('T')[0];
    }

    /**
     * توليد UUID
     */
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    /**
     * الحصول على حالة الترحيل
     */
    getStatus() {
        return this.migrationStatus;
    }

    /**
     * التحقق من إكمال الترحيل
     */
    isMigrationCompleted() {
        const completed = localStorage.getItem('dataMigrationCompleted');
        return completed ? JSON.parse(completed).completed : false;
    }
}

// إنشاء مثيل من نظام الترحيل المحسن
const migrationFix = new MigrationFix();

// تصدير النظام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = migrationFix;
} else {
    window.migrationFix = migrationFix;
}

// تشغيل الترحيل تلقائياً إذا لم يكن مكتملاً
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(async () => {
            try {
                if (!migrationFix.isMigrationCompleted() && migrationFix.checkForOldData()) {
                    console.log('🔄 بدء الترحيل التلقائي...');
                    const result = await migrationFix.startMigration();
                    if (result) {
                        console.log('✅ تم الترحيل التلقائي بنجاح');
                    } else {
                        console.log('⚠️ لم يتم العثور على بيانات للترحيل');
                    }
                } else {
                    console.log('ℹ️ الترحيل مكتمل مسبقاً أو لا توجد بيانات للترحيل');
                }
            } catch (error) {
                console.error('❌ خطأ في الترحيل التلقائي:', error);
            }
        }, 2000);
    });
}
