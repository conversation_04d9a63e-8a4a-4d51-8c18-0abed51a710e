# Update desktop shortcut to use start-app.bat
Write-Host "Updating desktop shortcut..." -ForegroundColor Yellow

try {
    $WshShell = New-Object -ComObject WScript.Shell
    $DesktopPath = [Environment]::GetFolderPath("Desktop")
    $CurrentPath = Get-Location
    
    $ShortcutPath = "$DesktopPath\Future Fuel Corporation.lnk"
    
    $Shortcut = $WshShell.CreateShortcut($ShortcutPath)
    $Shortcut.TargetPath = "$CurrentPath\start-app.bat"
    $Shortcut.WorkingDirectory = "$CurrentPath"
    $Shortcut.Description = "Future Fuel Corporation Management System"
    $Shortcut.IconLocation = "$CurrentPath\icons\app-icon.ico"
    $Shortcut.Save()
    
    Write-Host "Desktop shortcut updated successfully!" -ForegroundColor Green
    Write-Host "Now points to: start-app.bat" -ForegroundColor Cyan
    
} catch {
    Write-Host "Error updating shortcut: $($_.Exception.Message)" -ForegroundColor Red
}
